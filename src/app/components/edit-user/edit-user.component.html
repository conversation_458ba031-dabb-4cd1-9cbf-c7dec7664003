<div>
  <ion-header mode="ios">
    <ion-toolbar color="primary" mode="ios">
      <ion-title>{{ pageHeading | translate }}</ion-title>
      <ion-buttons slot="end">
        <ion-button (click)="closeModal()"> <ion-icon slot="icon-only" name="close"></ion-icon></ion-button>
      </ion-buttons>
    </ion-toolbar>
    <ion-progress-bar type="indeterminate" *ngIf="progressbar"></ion-progress-bar>
  </ion-header>

  <!-- Tabs navigation with styling to match the master-data page -->
  <ion-segment [(ngModel)]="segmentValue" mode="md" class="master-data-tabs" value="Details">
    <ion-segment-button value="Details" layout="icon-start" class="segment-button-full-width">
      <ion-label>User Details</ion-label>
    </ion-segment-button>
    <ion-segment-button value="Skills" layout="icon-start" class="segment-button-full-width">
      <ion-label>User Skills</ion-label>
    </ion-segment-button>
  </ion-segment>
    <div [ngSwitch]="segmentValue" class="tab-content">
    <div *ngSwitchCase="'Details'" class="details-tab-content">
      <div class="form-container">
        <div class="form-row">
          <div class="form-group">
            <ion-select style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;" interface="popover"
              placeholder="Please select a role" fill="outline" required="true" labelPlacement="stacked"
              [(ngModel)]="selectedRole" (ionChange)="clearError()">
              <div slot="label">Select Role <ion-text color="danger">*</ion-text></div>
              <ion-icon slot="start" name="people-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
              <ion-select-option *ngFor="let role of roles" [value]="role.ROLE_NAME">{{role.ROLE_NAME}}</ion-select-option>
            </ion-select>
            <div *ngIf="!isUpdatingContent && !selectedRole" style="font-size: 12px; color: #666; margin-top: -3px; margin-bottom: 8px;">
              Please select a role for this user
            </div>
          </div>

          <div class="form-group">
            <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;"
              placeholder="Enter User ID" [disabled]="true" [(ngModel)]="user.USER_ID"
              required="true" labelPlacement="stacked" fill="outline">
              <div slot="label">User ID <ion-text color="danger">*</ion-text></div>
              <ion-icon slot="start" name="id-card-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
            </ion-input>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;"
              placeholder="Enter First Name" [(ngModel)]="user.FIRST_NAME" (ionInput)="clearError()"
              required="true" labelPlacement="stacked" fill="outline">
              <div slot="label">First Name <ion-text color="danger">*</ion-text></div>
              <ion-icon slot="start" name="person-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
            </ion-input>
          </div>

          <div class="form-group">
            <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;"
              placeholder="Enter Last Name" [(ngModel)]="user.LAST_NAME" (ionInput)="clearError()"
              required="true" labelPlacement="stacked" fill="outline">
              <div slot="label">Last Name <ion-text color="danger">*</ion-text></div>
              <ion-icon slot="start" name="person-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
            </ion-input>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 2px;"
              placeholder="Enter Email" [disabled]="isUpdatingContent" [(ngModel)]="user.EMAIL"
              required="true" labelPlacement="stacked" fill="outline" type="email"
              [class.ion-invalid]="!emailValid || emailExists" [class.ion-valid]="emailValid && !emailExists"
              (ionInput)="onEmailInput(); clearError()" (ionBlur)="validateEmail()">
              <div slot="label">Email <ion-text color="danger">*</ion-text></div>
              <ion-icon slot="start" name="mail-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
            </ion-input>
            <div *ngIf="!emailValid" class="validation-error">
              {{ emailErrorMessage }}
            </div>
            <div *ngIf="emailExists" class="validation-error">
              User with same email already exists
            </div>
          </div>

          <div class="form-group">
            <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 2px;"
              placeholder="Enter Phone Number (xxx-xxx-xxxx)" [(ngModel)]="displayPhone"
              required="true" labelPlacement="stacked" fill="outline" maxlength="12"
              (ionInput)="formatPhoneNumber($event)" (ionBlur)="validatePhoneNumber()"
              [class.ion-invalid]="!phoneValid" [class.ion-valid]="phoneValid">
              <div slot="label">Phone Number <ion-text color="danger">*</ion-text></div>
              <ion-icon slot="start" name="call-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
            </ion-input>
            <div *ngIf="!phoneValid" class="validation-error">
              {{ phoneErrorMessage }}
            </div>
          </div>
        </div>

        <div class="approvals-section">
          <ion-select style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 5px;" interface="popover"
            placeholder="Select Approvals" fill="outline" labelPlacement="stacked" multiple="true"
            [(ngModel)]="selectedApprovals" (ionChange)="updateApprovalChecks()">
            <div slot="label">Select Approvals</div>
            <ion-icon slot="start" name="checkmark-circle-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
            <ion-select-option *ngFor="let approval of approvalsList" [value]="approval.APPR_TYPE">{{approval.DESCRIPTION}}</ion-select-option>
          </ion-select>

          <!-- Display selected approvals as badges -->
          <div class="approval-badges" *ngIf="selectedApprovals && selectedApprovals.length > 0">
            <ion-badge *ngFor="let approval of selectedApprovals" color="primary" class="approval-badge">
              {{ getApprovalDescription(approval) }}
              <ion-icon name="close-circle" (click)="removeApproval(approval)"></ion-icon>
            </ion-badge>
          </div>
        </div>
      </div>
    </div>
    <div *ngSwitchCase="'Skills'" class="skills-tab-content">
      <!-- Search card -->
      <div class="search-card">
        <div class="search-wrapper">
          <ion-icon name="search-outline"></ion-icon>
          <input type="text" placeholder="Search skills..." [(ngModel)]="skillSearchTerm" (input)="filterSkills()">
          <ion-icon name="close-outline" class="clear-search" *ngIf="skillSearchTerm" (click)="clearSkillSearch()"></ion-icon>
        </div>
        <div class="action-buttons">
          <button class="add-btn" (click)="openSkillModal(false, '')">
            <ion-icon name="add-outline"></ion-icon>
            Add Skill
          </button>
        </div>
      </div>

      <!-- Skills Grid -->
      <div class="custom-table">
        <!-- Table Header -->
        <div class="table-header">
          <div class="header-col skill-col">Skill</div>
          <div class="header-col rating-col">Rating</div>
          <div class="header-col cert-col">Certificate</div>
        </div>

          <!-- Skills List -->
          <div class="skills-list">
            <ion-card *ngFor="let skill of filteredSkills; index as i" class="skill-card unselected-card">
              <div class="table-row">
                <!-- Skill Type Column -->
                <div class="table-col skill-col">
                  <div class="skill-container">
                    <span>{{ getSkillDescription(skill.SKILL_TYPE) }}</span>
                  </div>
                </div>

                <!-- Rating Column -->
                <div class="table-col rating-col">
                  <div class="rating-display">
                    <ng-container *ngFor="let star of [1,2,3,4,5]; let j = index">
                      <ion-icon [name]="j < +skill.RATING ? 'star' : 'star-outline'" class="star-icon">
                      </ion-icon>
                    </ng-container>
                  </div>
                </div>

                <!-- Certificates Column -->
                <div class="table-col cert-col">
                  <div class="cert-content">
                    <ng-container *ngFor="let image of getFilteredDocs(skill.SKILL_TYPE); index as j">
                      <div *ngIf="image?.docItem?.FILE_NAME" class="certificate-item">
                        <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'EXCEL'"
                          class="doc-link" (click)="downloadFile(image); $event.stopPropagation();">
                          <ion-icon name="document-outline"></ion-icon>
                          {{ image.docItem.FILE_NAME }}
                        </a>

                        <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'PPT'"
                          class="doc-link" (click)="downloadFile(image); $event.stopPropagation();">
                          <ion-icon name="document-outline"></ion-icon>
                          {{ image.docItem.FILE_NAME }}
                        </a>

                        <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'WORD'"
                          class="doc-link" (click)="downloadFile(image); $event.stopPropagation();">
                          <ion-icon name="document-outline"></ion-icon>
                          {{ image.docItem.FILE_NAME }}
                        </a>

                        <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'DOCUMENT'"
                          class="doc-link" (click)="downloadFile(image); $event.stopPropagation();">
                          <ion-icon name="document-outline"></ion-icon>
                          {{ image.docItem.FILE_NAME }}
                        </a>

                        <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'PDF'"
                          class="doc-link" (click)="downloadFile(image); $event.stopPropagation();">
                          <ion-icon name="document-outline"></ion-icon>
                          {{ image.docItem.FILE_NAME }}
                        </a>

                        <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'TEXT'"
                          class="doc-link" (click)="downloadFile(image); $event.stopPropagation();">
                          <ion-icon name="document-outline"></ion-icon>
                          {{ image.docItem.FILE_NAME }}
                        </a>

                        <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'CSV'"
                          class="doc-link" (click)="downloadFile(image); $event.stopPropagation();">
                          <ion-icon name="document-outline"></ion-icon>
                          {{ image.docItem.FILE_NAME }}
                        </a>

                        <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'URL'"
                          [href]="image.docItem.FILE_NAME" target="_blank" rel="noopener noreferrer" class="doc-link"
                          (click)="$event.stopPropagation();">
                          <ion-icon name="link-outline"></ion-icon>
                          {{ image.docItem.TITLE }}
                        </a>

                        <span *ngIf="image?.DOC_TYPE === 'IMAGE'" class="doc-link">
                          <ion-icon name="image-outline"></ion-icon>
                          {{ image?.docItem?.FILE_NAME }}
                        </span>
                      </div>
                    </ng-container>
                  </div>
                  <!-- Row Action Buttons -->
                  <div class="row-actions">
                    <ion-button fill="clear" size="small" color="primary" (click)="openSkillModal(true, skill); $event.stopPropagation();">
                      <ion-icon name="create-outline"></ion-icon>
                    </ion-button>
                    <ion-button fill="clear" size="small" color="danger" (click)="deleteSelectedItem(skill, i); $event.stopPropagation();">
                      <ion-icon name="trash-outline"></ion-icon>
                    </ion-button>
                  </div>
                </div>
              </div>
            </ion-card>

            <!-- Empty state when no skills at all -->
            <div *ngIf="filteredSkills.length === 0 && (!userSkill || userSkill.length === 0)" class="empty-skills-message">
              <div class="no-results-content">
                <ion-icon name="construct-outline"></ion-icon>
                <h3 class="no-results-title">No Skills Added</h3>
                <p class="no-results-message-text">No skills have been added yet.</p>
                <p class="no-results-suggestion">Click 'Add Skill' to add skills to this user.</p>
              </div>
            </div>

            <!-- No search results -->
            <div *ngIf="filteredSkills.length === 0 && userSkill && userSkill.length > 0 && skillSearchTerm" class="no-results-message">
              <div class="no-results-content">
                <ion-icon name="search-outline"></ion-icon>
                <h3 class="no-results-title">No Skills Found</h3>
                <p class="no-results-message-text">
                  No skills match "<strong>{{skillSearchTerm}}</strong>".
                </p>
                <p class="no-results-suggestion">
                  Try adjusting your search terms or browse all skills below.
                </p>
                <button class="clear-search-btn" (click)="clearSkillSearch()">
                  <ion-icon name="close-circle-outline"></ion-icon>
                  Clear Search
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <p class="error">{{displayError}}</p>
</div>
<ion-footer mode="ios">
  <ion-toolbar mode="ios">
    <ion-button slot="end" color="danger" mode="md" (click)="closeModal()">Cancel</ion-button>
    <ion-button slot="end" color="success" mode="md" [disabled]="!user?.FIRST_NAME || !user?.LAST_NAME || !user?.EMAIL || !user?.PHONE || !selectedRole || !emailValid || !phoneValid || emailExists" (click)="addOrUpdateUser(user)">Save</ion-button>
  </ion-toolbar>
</ion-footer>
