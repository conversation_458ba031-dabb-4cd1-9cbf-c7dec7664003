import { ChangeDetector<PERSON><PERSON>, <PERSON>mponent, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { NavigationExtras, Router } from '@angular/router';
import { Network } from '@awesome-cordova-plugins/network/ngx';
import * as hash from 'object-hash';
import {
  DbResult,
  RequestType,
  ResultType,
  SyncResult,
  UnviredCordovaSDK,
} from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import {
  AlertController,
  InfiniteScrollCustomEvent,
  LoadingController,
  ModalController,
  Platform,
  PopoverController,
  ToastController,
} from '@ionic/angular';
import { Subscription, catchError } from 'rxjs';
import {
  Column,
  DOCUMENT_ATTACHMENT,
  DOCUMENT_HEADER,
  FormComponent,
  PERMIT_DOC,
  PERMIT_FORM,
  PERMIT_HEADER,
  PERMIT_LOG,
  PERMIT_QUERY_CTX_HEADER,
  PERMIT_STAKEHOLDER,
  USER_CONTEXT_HEADER,
} from 'src/app/data-models/data_classes';
import { FormRenderPage } from 'src/app/pages/form-render/form-render.page';
import { FORM_HEADER } from 'src/app/services/HEADER';
import { ShareDataService } from 'src/app/services/ShareData.service';
import { DataService } from 'src/app/services/data.service';
import {
  AppConstants,
  PermitStatus,
  PermitUserRole,
} from 'src/app/shared/app-constants';
import { AddNewPermitStakeHolderComponent } from '../add-new-permit-stake-holder/add-new-permit-stake-holder.component';
import * as moment from 'moment';
import { HelperFunctionService } from 'src/app/services/HelperFunction.service';
import { AppSpecificUtilityService } from 'src/app/services/app-specific-utility.service';
import { BusyIndicatorService } from 'src/app/services/busy-indicator.service';
import { TranslateService } from '@ngx-translate/core';
import { Geolocation } from '@awesome-cordova-plugins/geolocation/ngx';
import { OptionsPopoverPage } from 'src/app/pages/options-popover/options-popover.page';
import { AttachmentsService } from 'src/app/services/attachments.service';
import { Camera, CameraOptions } from '@awesome-cordova-plugins/camera/ngx';
import imageCompression from 'browser-image-compression';
import { ImageFullScreenComponent } from '../image-full-screen/image-full-screen.component';
import { AnnotateImageComponent } from '../annotate-image/annotate-image.component';
// import { IButtonGroupEventArgs, IgxStepType } from 'igniteui-angular';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { SkillsCertificateViewComponent } from '../skills-certificate-view/skills-certificate-view.component';
import { EditPermitDetailsComponent } from '../edit-permit-details/edit-permit-details.component';
import { EditPermitAgentsComponent } from '../edit-permit-agents/edit-permit-agents.component';
import { EditPermitValidityComponent } from '../edit-permit-validity/edit-permit-validity.component';
import { AddPermitCommentsComponent } from '../add-permit-comments/add-permit-comments.component';
import { FacilitiesDivisionsPageModule } from 'src/app/pages/facilities-divisions/facilities-divisions.module';
import { AddPermitRoleComponent } from '../add-permit-role/add-permit-role.component';
import { RejectPermitComponent } from '../reject-permit/reject-permit.component';
import { EditFormComponent } from 'src/app/components/edit-form/edit-form.component';
import { UserSkillsTooltipComponent } from '../user-skills-tooltip/user-skills-tooltip.component';

declare let window: any;
declare let cordova: any;
declare const loadForm: any;
declare const submit: any;
declare const wizardSubmit: any;
declare const returnTempData: any;
declare const returnLastTempData: any;
declare const setLocation: any;

@Component({
  selector: 'app-permit-details',
  templateUrl: './permit-details.component.html',
  styleUrls: ['./permit-details.component.scss'],
})
export class PermitDetailsComponent implements OnInit, OnDestroy {
  public userRole: string;
  public permit: any;
  public isReport: boolean;
  public isShowProgressBar: boolean = false;
  public isDetailsLoading: boolean = false;
  public isPartnersLoading: boolean = false;
  public isFormLoading: boolean = false;
  public isInitialLoad: boolean = true;
  public isPermitreadOnly: boolean = false;
  public priorities = [];
  public capturedSnapURL: string;
  public devicePlatform = 'browser';
  public tempPermitObj: any[] = [];
  public rcaList: any[] = [];
  public defaultSelectedRca: any[] = [];
  private subscriptions = new Subscription();
  public requestUser: string = '';
  public reviewUser: string = '';
  public reviewForm: FormGroup;
  public otherReviews: any[] = [];
  public segmentValue: string = 'details';
  public stakeHoldersList: any[] = [];
  public initialStakeholderlist: any[] = []
  public usersList: any[] = [];
  public permitDocs: any[] = [];
  public filteredInternalList: any[] = [];
  public filteredExternalList: any[] = [];
  public calculatedPercentage = 0;
  public isHybridNative: boolean;
  public setBarcode: any = false;
  public componentID: string = '';
  private lastTimeBackPress = 0;
  private timePeriodToExit = 2000;
  public locationComponentID: string = '';
  public attributesJson: any;
  public tempData: any;
  public isdisableFormCompletion: boolean = false;
  public formReadOnlyFlag: boolean = false;
  public buttonType: string;
  public formReadWriteFlag: boolean = false;
  public FirstTimeFlag: boolean = false;
  public currentUserDetails: any;
  public isAndroid: boolean = false;
  public formData: any;
  public formViewOnlyFlag: boolean = false;
  public isFormLoaded: boolean = false;
  public subData: any;
  public translateJson: any;
  public componentObj: any;
  public cameraOpen: boolean = false;
  public form: FORM_HEADER;
  public formDescription: string = '';
  public masterData: any;
  public formSubmissionData: any;
  public markComplete: boolean = false;
  public completeFlag: boolean = false;
  public permitFormSubmissionData = {} as PERMIT_FORM;
  public permitHeaderData = {} as PERMIT_HEADER;
  public constants: AppConstants;
  public formLoaded = false;
  public facilityName: string = '';
  public permitForm = {} as PERMIT_FORM;
  public isFormFound: boolean = true
  public userObject: USER_CONTEXT_HEADER;
  public isMobile: boolean = false;
  public isHideNewStakeHolderButton: boolean = false;
  public isloaded: boolean = false;
  disableTime: Date | null = null;
  isToggleEnabled: boolean = false; // Tracks if toggle should be enabled
  toggleState: boolean = false; // Tracks the state of the toggle
  extensionForm: FormGroup;

  approvalCompleted: boolean;
  rejectionCompleted: boolean;
  isModalOpen = false; // Prevent multiple modal openings
  addReason: boolean

  public filteredUsers = [];
  cameraOptions: CameraOptions = {
    quality: 100,
    destinationType: this.camera.DestinationType.DATA_URL,
    encodingType: this.camera.EncodingType.JPEG,
    mediaType: this.camera.MediaType.PICTURE,
  };

  public minDate = new Date().toISOString();
  maxEndDate: string | null = null;
  minEndDate: string | null = null;



  public internalApprType: any = [];
  public externalApprType: any = [];


  stakeholderData: any;
  rejectedComment: string;
  stakeActionStatusArray: any[] = []
  dataisLoading: boolean;
  isLoading: boolean = false;
  resultErrorMessage: string;
  allUsers: any[] = []
  actionButtonClicked: boolean = false;
  showFillFormButton: boolean;
  previousSegmentValue: string = 'details';

  showSaveButton: boolean = false;
  selectedStakeholder: any
  stakeHolderActionStatus: string = ''
  userRoleData: any;
  isPermitRevised: boolean;
  selectedSegment: string = 'default';

  // Computed properties for filtering stakeholders
  get currentStakeholders() {
    if (!this.isPermitRevised) {
      return this.stakeHoldersList || [];
    }

    if (!this.stakeHoldersList || this.stakeHoldersList.length === 0) {
      return [];
    }

    return this.stakeHoldersList.filter(stakeholder => stakeholder.IS_ACTIVE === 'X');
  }

  get historyStakeholders() {
    if (!this.isPermitRevised) {
      return [];
    }

    if (!this.stakeHoldersList || this.stakeHoldersList.length === 0) {
      return [];
    }

    return this.stakeHoldersList.filter(stakeholder => stakeholder.IS_ACTIVE !== 'X');
  }



  // public stepType: IgxStepType = 'full';
  // public stepTypes: any[] = [
  //     {
  //         label: 'Indicator', stepType: 'indicator',
  //         selected: this.stepType === 'indicator', togglable: true
  //     },
  //     {
  //         label: 'Title', stepType: 'title',
  //         selected: this.stepType === 'title', togglable: true
  //     },
  //     {
  //         label: 'Full', stepType: 'full',
  //         selected: this.stepType === 'full', togglable: true
  //     }
  // ];
  constructor(
    private modalController: ModalController,
    private unviredSDK: UnviredCordovaSDK,
    public dataService: DataService,
    public sanitizer: DomSanitizer,
    private shareData: ShareDataService,
    public formBuilder: FormBuilder,
    private loader: BusyIndicatorService,
    private translate: TranslateService,
    private appSpecificUtility: AppSpecificUtilityService,
    private geolocation: Geolocation,
    private utilityFunction: HelperFunctionService,
    public popoverCtrl: PopoverController,
    private alertController: AlertController,
    private platform: Platform,
    private router: Router,
    public loadingController: LoadingController,
    public attachmentService: AttachmentsService,
    private network: Network,
    private camera: Camera,
    private http: HttpClient,
    private ngZone: NgZone,
    private cdr: ChangeDetectorRef
  ) {
    this.reviewForm = this.formBuilder.group({});
    this.extensionForm = this.formBuilder.group({
      startDate: [new Date().toISOString(), Validators.required],
      endDate: ['', Validators.required],
    });


    this.updateEndDateRange(new Date(this.extensionForm.controls['startDate'].value));
  }


  ngOnDestroy(): void {
    console.log('Inspection detail component killed', this.permit);
    this.subscriptions.unsubscribe();
  }

  async ngOnInit() {
    console.log('ngOnInit called - permit details', this.permit);

    this.permit.IS_EXTENDED = (this.permit.IS_EXTENDED == 'true' || this.permit.IS_EXTENDED == true) ? true : false;
    
    this.devicePlatform = this.dataService.getDevicePlatform();
    this.isMobile = this.devicePlatform === 'browser' ? false : true;

    this.setMinDate();

    // Check if location coordinates are available for this permit
    await this.checkAndSetCoordinates();
    // await this.getUsersList();
    let userContextResult = await this.dataService.getData(
      'USER_CONTEXT_HEADER'
    );
    this.userObject = userContextResult[0];

    this.userRoleData = await this.dataService.getData('USER_ROLE');
    console.log(this.userRoleData);

    // Create a clean PERMIT_QUERY_CTX_HEADER without display-only properties like permitTypeInfo
    let permitQueryCtxHeader = new PERMIT_QUERY_CTX_HEADER();
    permitQueryCtxHeader.PERMIT_NO = this.permit.PERMIT_NO;
    permitQueryCtxHeader.PERMIT_TYPE = this.permit.PERMIT_TYPE;
    permitQueryCtxHeader.DESCRIPTION = this.permit.DESCRIPTION;
    permitQueryCtxHeader.FACILITY_ID = this.permit.FACILITY_ID;
    permitQueryCtxHeader.STATUS = this.permit.STATUS;
    permitQueryCtxHeader.TAG = this.permit.TAG;

    let inspectionHeaderInput = {
      PERMIT_QUERY_CTX: [{ PERMIT_QUERY_CTX_HEADER: permitQueryCtxHeader }],
    };
    let getPermitsResponse: any = await this.dataService.getpermits(
      inspectionHeaderInput,
      RequestType.QUERY,
      true
    );

    if (getPermitsResponse.type == ResultType.success) {
      // Update the permit object with fresh data from server if available
      if (getPermitsResponse.data && getPermitsResponse.data.PERMIT && getPermitsResponse.data.PERMIT.length > 0) {
        const updatedPermit = getPermitsResponse.data.PERMIT[0].PERMIT_HEADER;
        if (updatedPermit) {
          // Preserve any local UI properties that shouldn't be overwritten
          const permitTypeInfo = this.permit.permitTypeInfo;
          const isShowDetailsButton = this.permit.isShowDetailsButton;
          const isShowReviseButton = this.permit.isShowReviseButton;

          // Update with server data
          this.permit = { ...this.permit, ...updatedPermit };

          // Restore local UI properties
          if (permitTypeInfo) this.permit.permitTypeInfo = permitTypeInfo;
          if (isShowDetailsButton !== undefined) this.permit.isShowDetailsButton = isShowDetailsButton;
          if (isShowReviseButton !== undefined) this.permit.isShowReviseButton = isShowReviseButton;
        }
      }

      console.log('permit AFTER THE CALL', this.permit);
      if (this.permit) {
        this.permit.PERMIT_DATE = +this.permit.PERMIT_DATE;
        this.permit.EXPIRY_DATE = +this.permit.EXPIRY_DATE;
      }

      // Load permit docs after API call completes and data is saved to database
      await this.getPermitDocs();

      return {
        type: ResultType.success,
        error: '',
        data: getPermitsResponse?.data,
      };
    } else {
      return { type: ResultType.error, error: getPermitsResponse.message };
    }
  }
  async ionViewWillEnter() {
    console.log('ionViewWillEnter called');
    this.devicePlatform = this.shareData.getDevicePlatform();
    this.devicePlatform =
      this.devicePlatform == '' || this.devicePlatform == undefined
        ? 'browser'
        : this.devicePlatform;
    this.isHybridNative = this.devicePlatform === 'browser' ? false : true;
    // Set initial loading states
    this.isInitialLoad = true;
    this.isDetailsLoading = true;
    this.isPartnersLoading = true;
    this.isFormLoading = true;
    await this.getFormTempaltes();
    await this.createFormAndSetFormData();

    // After successfull scan |SCANNER RETURNED DATA|
    let component = this;
    this.shareData.getBackToRenderer.subscribe((data) => {
      component.setBarcode = false;
      setTimeout(async () => {
        component.ionViewDidEnter();
        if (data !== '') {
          this.shareData.setNavigateFromCameraPage(true);
          // fire the scanned data event
          let eventCustom = new CustomEvent('barcodeScannedData', {
            detail: {
              componentID: component.componentID,
              scannedData: data,
            },
          });

          document.dispatchEvent(eventCustom);
        } else {
          let barcodeChange = new CustomEvent('barcodeChangeEvent', {
            detail: {},
          });
          document.dispatchEvent(barcodeChange);
        }
      }, 800);
    });

    // listener for the form mandatory fields completion percentage alert
    document.addEventListener('BtnAction', async (event: any) => {
      let previousVal = this.calculatedPercentage;
      // localStorage.setItem('eventPer', event.detail.calculationPercentage);
      await this.setButtonType(
        event.detail.tempData,
        event.detail.calculationPercentage
      );

      this.calculatedPercentage = Math.round(
        event.detail.calculationPercentage
      );
      if (
        event.detail.calculationPercentage == 100 &&
        event.detail.errObj.length == 0
      ) {
      } else if (event.detail.errObj.length > 0) {
      }
      let ele = document.getElementById('badge');
      var gradColor1 = `${'#90EE90'} ${this.calculatedPercentage}%`;
      var gradColor2 = `${'#ffffff'} ${100 - this.calculatedPercentage}%`;
      if (this.calculatedPercentage == 0) {
        if (ele && ele.style) ele.style.background = '#ffffff';
      } else {
        if (ele && ele.style)
          ele.style.background = `linear-gradient(to right, ${gradColor1}, ${gradColor2})`;
      }
      // update on every change
      // this.initialisePercentageLoader(previousVal);
      // this.setButtonType();
    });

    document.addEventListener('click', () => {
      setTimeout(() => {
        let ele = document.querySelector('.renderOptions .popover-content');
        if (ele && ele.getAttribute('id')) {
          let eleWidth = parseFloat(getComputedStyle(ele).width);
          let top = parseFloat(getComputedStyle(ele).top);
          ele.removeAttribute('id');
          if (window.innerWidth) {
            ele.setAttribute(
              'style',
              `left: ${window.innerWidth - eleWidth
              }px !important; top: ${top}px !important; transform-origin: right top;`
            );
          }
        }
      }, 100);
    });

    this.platform.ready().then(() => {
      this.platform.backButton.subscribeWithPriority(0, () => {
        if (
          moment().valueOf() - this.lastTimeBackPress >=
          this.timePeriodToExit
        ) {
          this.lastTimeBackPress = moment().valueOf();
          // if (this.router.url == "/settings") {
          //   // Don't check for go back
          //   this.router.navigate(['/permit'])
          // } else {
          //   this.goBackToLastLocation();
          // }
        } else {
          // Ignore. This implies multiple events in quick succession.
          // This is to account for false events.
        }
      });
    });
    if (this.loader.isLoading) {
      this.unviredSDK.logInfo(
        'PermitDetailsComponent',
        'ionViewWilEnter()',
        'Hiding Busy indicator...'
      );
      if (this.loader.isLoading) {
        await this.loader.dismissBusyIndicator();
      }
    }
    // if (this.devicePlatform != '' && !this.loader.isLoading) {
    //   await this.loader.showBusyIndicator(this.translate.instant('Loading form...'), 'crescent');
    // }
    let page = document.querySelector('app-form-renderer');
    if (page) {
      page.setAttribute('id', 'render-page');
    }
    let _self = this;
    document.addEventListener(
      'getLocationFromApp',
      async function (event: any) {
        _self.locationComponentID = event.detail.controlId;
        console.log('getLocationFromApp event listening');
        // browser
        if (_self.devicePlatform === 'browser') {
          let location =
            await _self.appSpecificUtility.getLocationCoordinates();
          setLocation(location, _self.locationComponentID);
        } else {
          // mobile
          let opt = { enableHighAccuracy: true };

          await _self.geolocation
            .getCurrentPosition(opt)
            .then((resp) => {
              console.log('opt');
              console.log(resp);
              if (resp && resp.coords) {
                let locationParam: string = '';
                locationParam = `${resp.coords.latitude.toString()}, ${resp.coords.longitude.toString()}`;
                setLocation(locationParam, _self.locationComponentID);
              }
            })
            .catch((err) => {
              console.log('ERROR!');
              console.log(err);
            });
        }
      }
    );

  }

  async ionViewDidEnter() {
    if (this.isReport) {
      let permitdetails = localStorage.getItem('permitDetailsFromreport');
      permitdetails = JSON.parse(permitdetails);
      if (permitdetails && permitdetails['PERMIT_FORM'] && permitdetails['PERMIT_FORM'].length > 0) {
        this.permitForm = permitdetails['PERMIT_FORM'][0];

        this.formSubmissionData = JSON.parse(this.permitForm.DATA);
      }
      // await this.getFormTempaltes();
      if (permitdetails && permitdetails['PERMIT_STAKEHOLDER'] && permitdetails['PERMIT_STAKEHOLDER'].length > 0) {
        this.stakeHoldersList = permitdetails['PERMIT_STAKEHOLDER'];
      }
      await this.getAllStakeHolders();


    } else {
      await this.getAllStakeHolders();
    }

    this.isShowProgressBar = true;

    await this.getFacilityNameById();

    // Clear loading states after all data is loaded
    this.isDetailsLoading = false;
    this.isInitialLoad = false;

    this.initialStakeholderlist = JSON.parse(JSON.stringify(this.stakeHoldersList))
    console.log("this.initialStakeholderlist", this.initialStakeholderlist)

    if (this.permit.APPROVAL_SUMMARY == 'R') {
      this.isHideNewStakeHolderButton = true
    }


    const reviewCount = this.stakeHoldersList.filter(
      (stakeholder) => stakeholder.ROLE === 'REVIEW' && stakeholder.APPR_TYPE === null
    ).length;

    if (reviewCount > 1) {
      this.isPermitRevised = true;
    } else {
      this.isPermitRevised = false;
    }



  }


  setMinDate() {
    // this.minDate = new Date().toISOString();
    let permitEndTime = this.permit.EXPIRY_DATE; // Predefined end time in milliseconds
    let enableTime: Date | null = null; // Time when toggle will be enabled



    enableTime = new Date(permitEndTime);
    this.disableTime = new Date(permitEndTime + 8 * 60 * 60 * 1000); // 8 hours from endTime

    let toggleEnabledTime: Date = new Date(permitEndTime + 8 * 60 * 60 * 1000);
    if (this.permit.IS_EXTENDED == true) {

      const now = new Date().getTime();

      this.isToggleEnabled = now >= permitEndTime && now < permitEndTime + 8 * 60 * 60 * 1000;

    }
  }

  async getPermitDocs() {

    let getPermitDocsquery = `SELECT * FROM document_header dh JOIN permit_doc pd ON dh.DOC_ID = pd.DOC_ID WHERE pd.PERMIT_NO ='${this.permit.PERMIT_NO}'`;

    let getPermitDocsqueryResult = await this.unviredSDK.dbExecuteStatement(getPermitDocsquery);


    if (getPermitDocsqueryResult?.data?.length > 0) {
      for (let ri = 0; ri < getPermitDocsqueryResult.data.length; ri++) {
        if (
          getPermitDocsqueryResult.data[ri].THUMBNAIL &&
          getPermitDocsqueryResult.data[ri].THUMBNAIL != null
        ) {
          if (
            getPermitDocsqueryResult.data[ri].THUMBNAIL.includes(
              'data:image/png;base64,'
            ) ||
            getPermitDocsqueryResult.data[ri].THUMBNAIL.includes(
              'data:image/jpeg;base64,'
            ) ||
            getPermitDocsqueryResult.data[ri].THUMBNAIL.includes(
              'data:image/jpg;base64,'
            )
          ) {
          } else {
            getPermitDocsqueryResult.data[ri].THUMBNAIL =
              this.sanitizer.bypassSecurityTrustResourceUrl(
                `data:image/jpg;base64,${getPermitDocsqueryResult.data[ri].THUMBNAIL}`
              );
          }
          this.permitDocs.push({
            thumbnail: getPermitDocsqueryResult.data[ri].THUMBNAIL,
            file: null,
            docItem: getPermitDocsqueryResult.data[ri],
            DOC_TYPE: getPermitDocsqueryResult.data[ri].DOC_TYPE
          });
        } else {
          this.permitDocs.push({
            thumbnail: getPermitDocsqueryResult.data[ri].THUMBNAIL,
            file: null,
            docItem: getPermitDocsqueryResult.data[ri],
            DOC_TYPE: getPermitDocsqueryResult.data[ri].DOC_TYPE
          });
        }
      }
      console.log("the permit docs is ", this.permitDocs)
    } else {
      this.permitDocs = [];
    }
  }

  async getFacilityNameById() {
    let getFacilityNameByIdQuery = `SELECT NAME AS NAME FROM FACILITY_HEADER WHERE FACILITY_ID='${this.permit.FACILITY_ID}'`;
    let getFacilityNameByIdQueryResult =
      await this.unviredSDK.dbExecuteStatement(getFacilityNameByIdQuery);
    this.facilityName = getFacilityNameByIdQueryResult?.data[0]?.NAME;
    getFacilityNameByIdQueryResult?.data?.length > 0
      ? getFacilityNameByIdQueryResult?.data[0]?.NAME
      : this.permit.FACILITY_ID;
  }

  async closeModal() {
    let hasStatusChange

    if (JSON.stringify(this.stakeHoldersList) == JSON.stringify(this.initialStakeholderlist)) {
      console.log("No data changed");
    } else {
      console.log("Data changed");

      hasStatusChange = true;


    }
    // }



    if (this.stakeActionStatusArray.length > 0 || hasStatusChange) {
      console.log("data changed");
        const alert = await this.alertController.create({
              header: 'Discard Changes?',
              message: 'You have unsaved changes in Partners. Do you want to discard them' + '?',
              buttons: [
                {
                  text: 'No',
                  role: 'cancel',
                  cssClass: 'secondary',
                  handler: (blah) => { },
                },
                {
                  text: 'Yes',
                  role: 'confirm',
                  handler: async () => {
                    await this.discardLocalChanges();
                   
                    this.modalController.dismiss();
              
                  }
                },
              ],
            });
            await alert.present();
  } else {
    this.modalController.dismiss();
  }

  }

  async cancelPermit(permit: any) {
    const alert = await this.alertController.create({
      cssClass: 'my-custom-class',
      header: 'Are sure you want to cancel the permit?',
      backdropDismiss: false,
      inputs: [
        {
          type: 'textarea',
          placeholder: 'Enter Reason',
          name: 'reason',
        },
      ],
      buttons: [
        {
          text: 'No',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => { },
        },
        {
          text: 'Yes',
          handler: async (alertData) => {
            // Validate that reason is provided
            if (!alertData.reason || alertData.reason.trim().length === 0) {
              const validationAlert = await this.alertController.create({
                header: 'Validation Error',
                message: 'Please enter a reason for cancelling the permit.',
                buttons: ['OK']
              });
              await validationAlert.present();
              return false; // Prevent dialog from closing
            }

            try {
              // Show shimmer effect instead of please wait loader
              this.isLoading = true;

              let inspectionUpdateQuery = `UPDATE PERMIT_HEADER SET P_MODE = 'M', OBJECT_STATUS = 2 WHERE PERMIT_NO = '${permit.PERMIT_NO}'`;
              let inspectionUpdateResult = await this.unviredSDK.dbExecuteStatement(inspectionUpdateQuery);

              if (inspectionUpdateResult.type == ResultType.success) {
                permit.P_MODE = 'M';
                permit.OBJECT_STATUS = 2;
              }

              let user = '';
              let userContext: any = localStorage.getItem('userContext');
              if (userContext) {
                userContext = JSON.parse(userContext);
              }

              if (userContext?.USER_CONTEXT_HEADER) {
                user = userContext?.USER_CONTEXT_HEADER?.FIRST_NAME + ' ' + userContext?.USER_CONTEXT_HEADER?.LAST_NAME;
              }

              let comment = `${user} cancelled permit on ${this.getCurrentTime()} with a reason ${alertData.reason}`;

              let permitLog = new PERMIT_LOG();
              permitLog.LOG_NO = await this.findMaxLogNumberFromDb(permit.PERMIT_NO);
              permitLog.LID = this.unviredSDK.guid().replace(/-/g, '');
              permitLog.SYNC_STATUS = 0;
              permitLog.OBJECT_STATUS = 1;
              permitLog.CREATED_ON = new Date().getTime();
              permitLog.P_MODE = 'A';
              permitLog.PERMIT_NO = permit.PERMIT_NO;
              permitLog.FID = permit.LID;
              permitLog.PERMIT_STATUS = 'CANCELLED';
              permitLog.COMMENT = comment;
              permitLog.CREATED_BY = user;
              permitLog.ACTION = 'TRANSITION';

              await this.unviredSDK.dbInsert('PERMIT_LOG', permitLog, false);

              // Submit the permit with cancelled status
              await this.regularSubmitPermit(permit);

              // Hide shimmer effect
              this.isLoading = false;

              // Update permit status locally for immediate UI feedback
              this.permit.STATUS = 'CANCELLED';

              // Show success alert within the permit details
              const successAlert = await this.alertController.create({
                header: 'Success',
                message: 'Permit has been cancelled successfully.',
                buttons: ['OK']
              });
              await successAlert.present();

            } catch (error) {
              console.error('Error cancelling permit:', error);

              // Hide shimmer effect
              this.isLoading = false;

              // Show error alert within the permit details
              const errorAlert = await this.alertController.create({
                header: 'Error',
                message: 'Failed to cancel permit. Please try again.',
                buttons: ['OK']
              });
              await errorAlert.present();
            }
          },
        },
      ],
    });

    await alert.present();
  }

  getCurrentTime() {
    const currentDate = new Date();
    const options: any = {
      day: '2-digit',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    };
    return currentDate.toLocaleString('en-US', options);
  }

  async getFormTempaltes() {
    let getFormsResp: any = await this.dataService.getFormTemplates();
    if (getFormsResp.type == ResultType.success) {

    } else {
      console.log("error response on get templates")
      return { type: ResultType.error, error: getFormsResp.message };
    }
    if (this.isReport) {
      let formQuery = `SELECT * from FORM_HEADER`;
      let formQuerysResult = await this.unviredSDK.dbExecuteStatement(
        formQuery
      );
      if (formQuerysResult.type == ResultType.success) {
        if (formQuerysResult && formQuerysResult.data && formQuerysResult.data.length > 0) {
          for (let i = 0; i < formQuerysResult.data.length; i++) {
            let formHeader = formQuerysResult.data[i];
            if (formHeader && this.permitForm.FORM_ID === formHeader.FORM_ID) {
              let data = {
                type: ResultType.success,
                error: '',
                form: formHeader,
              };
              this.shareData.setFormData(data);
            }
          }
        }
      } else {
        return { type: ResultType.error, error: formQuerysResult.message };
      }
    }
    else {
      let permitFormQuery = `SELECT * from PERMIT_FORM WHERE PERMIT_NO = '${this.permit.PERMIT_NO}' AND (P_MODE !='D' OR P_MODE IS NULL)`;
      let permitFormQuerysResult = await this.unviredSDK.dbExecuteStatement(
        permitFormQuery
      );
      if (permitFormQuerysResult.type == ResultType.success) {
        if (permitFormQuerysResult && permitFormQuerysResult.data.length > 0) {
          let formQuery = `SELECT * from FORM_HEADER`;
          let formQuerysResult = await this.unviredSDK.dbExecuteStatement(
            formQuery
          );
          if (formQuerysResult.type == ResultType.success) {
            if (formQuerysResult && formQuerysResult.data && formQuerysResult.data.length > 0) {
              for (let i = 0; i < formQuerysResult.data.length; i++) {
                let formHeader = formQuerysResult.data[i];
                if (formHeader && permitFormQuerysResult.data[0].FORM_ID === formHeader.FORM_ID) {
                  let data = {
                    type: ResultType.success,
                    error: '',
                    form: formHeader,
                  };
                  this.shareData.setFormData(data);
                  await this.getPermitForm(formHeader.FORM_ID)
                }
              }
            }
          } else {
            return { type: ResultType.error, error: formQuerysResult.message };
          }
        }
      }
    }
  }

  async getAllStakeHolders() {
    this.stakeHoldersList = [];
    let getAllStakeHoldersQuery = `select * from PERMIT_STAKEHOLDER WHERE PERMIT_NO = '${this.permit.PERMIT_NO}' ORDER BY cast(SEQ_NO as unsigned) ASC`;
    let getAllStakeHoldersResult = await this.unviredSDK.dbExecuteStatement(
      getAllStakeHoldersQuery
    );
    if (getAllStakeHoldersResult && getAllStakeHoldersResult.data.length > 0) {

      this.stakeHoldersList = getAllStakeHoldersResult.data;
      switch (this.permit.STATUS) {
        case PermitStatus.APPROVED:
          let count = 0;
          this.stakeHoldersList.forEach(element => {
            if (element.ROLE == 'ISSUE') { count = count + 1 }
          });
          this.isHideNewStakeHolderButton = count > 0 ? true : false;
          break;
      }

      for await (let element of this.stakeHoldersList) {
        element['users'] = [];
        let getUsersQuery = '';
        this.usersList = [];
        if (this.permit.AGENT_ID_EXT != null && this.permit.AGENT_ID_INT != null) {
          getUsersQuery = `select B.first_name,B.last_name,B.USER_ID,B.role_name,${element.ROLE}
        from role_header as A join user_header as B on A.role_name=B.role_name
          and B.User_id in (select user_id from agent_user_header where agent_id in ('${this.permit.AGENT_ID_EXT}','${this.permit.AGENT_ID_INT}'))
          where ${element.ROLE} = 'true'`;
        } else if (
          this.permit.AGENT_ID_EXT != null &&
          this.permit.AGENT_ID_INT == null
        ) {
          getUsersQuery = `select B.first_name,B.last_name,B.USER_ID,B.role_name,${element.ROLE}
        from role_header as A join user_header as B on A.role_name=B.role_name
          and B.User_id in (select user_id from agent_user_header where agent_id in ('${this.permit.AGENT_ID_EXT}'))
          where ${element.ROLE} = 'true'`;
        } else {
          getUsersQuery = `select B.first_name,B.last_name,B.USER_ID,B.role_name,${element.ROLE}
        from role_header as A join user_header as B on A.role_name=B.role_name
          and B.User_id in (select user_id from agent_user_header where agent_id in ('${this.permit.AGENT_ID_INT}'))
          where ${element.ROLE} = 'true'`;
        }

        let fetchApproveUsersQueryResult = await this.unviredSDK.dbExecuteStatement(
          getUsersQuery
        );
        if (fetchApproveUsersQueryResult?.data?.length > 0) {
          this.usersList = fetchApproveUsersQueryResult?.data;
          this.filteredUsers.push(this.usersList)
          element['users'] = [...new Map(this.usersList.map(item => [item['USER_ID'], item])).values()]

        }
      }



    }

    await this.filterDataForPartners()
  }

  getName(USER_ID: any) {
    let res = this.allUsers.find(user => user.USER_ID === USER_ID);
    if (res) {
      return `${res.FIRST_NAME} ${res.LAST_NAME}`
    } else return USER_ID;
  }

  async addNewStakeHolderRole(editPartnerData: boolean, index: number, stakeHolderData?: any,) {
    if (this.isModalOpen) {
      console.log("Modal is already open. Preventing duplicate execution.");
      return; // Prevent double execution
    }
    if (stakeHolderData &&
      ['EXECUTE', 'APPROVE', 'ISSUE', 'CLOSE', 'CANCEL'].includes(stakeHolderData.ROLE) &&
      stakeHolderData.OBJECT_STATUS == undefined &&
      stakeHolderData.SYNC_STATUS == undefined) {
      console.log("Cannot edit this stakeholder type - FID and LID exist");
      return;
    }


    const modal = await this.modalController.create({
      component: AddNewPermitStakeHolderComponent,
      id: 'newStakeHolderRolePopup',
      cssClass: this.isMobile ? '' : 'newStakeHolderRolePopup',
      mode: 'ios',
      backdropDismiss: false,
      componentProps: {
        stakeData: stakeHolderData,
        editPartner: editPartnerData,
        permit: this.permit,
        stakeholderList: this.stakeHoldersList,
        isPermitRevised: this.isPermitRevised,
      },
    });



    let rejectedIndex = this.stakeHoldersList.findIndex(item => item.actionButtonStatus === "Rejected");


    if (!this.isPermitIsExpired(this.permit) && this.permit.APPROVAL_SUMMARY != 'R') {
      if (stakeHolderData.ROW_ID != 1 && !(stakeHolderData.ROLE == 'REVIEW' && stakeHolderData.APPR_TYPE == null)) {
        if (stakeHolderData.actionButtonStatus == null || stakeHolderData.actionButtonStatus == '')
          this.isModalOpen = true; // Set flag to true when modal is opening
        await modal.present();

      }

    }

    modal.onDidDismiss().then(async ({ data, role }) => {


      // Ensure isModalOpen is set to false
      this.isModalOpen = false;

      if (role === 'cancel') {
        return;
      }

      if (data && data.user_id) {

        stakeHolderData.USER_ID = data.user_id;
        stakeHolderData.P_MODE = 'M';
        stakeHolderData.OBJECT_STATUS = 1;

        this.stakeHoldersList[index].USER_ID = data.user_id
        this.stakeHoldersList[index].P_MODE = 'M'
        this.stakeHoldersList[index].OBJECT_STATUS = 1

        const rowId = stakeHolderData.ROW_ID;
  this.stakeActionStatusArray = this.stakeActionStatusArray.filter(item => item.ROW_ID !== rowId);

        this.cdr.detectChanges();
        // this.selectedStakeholder = stakeHolderData;
        console.log("this.previousSegmentValue", this.previousSegmentValue, this.segmentValue)
        if (this.previousSegmentValue == 'details' && this.segmentValue == 'form' && this.actionButtonClicked == false && this.selectedStakeholder != undefined) {
          //  await this.createFormAndSetFormData()
        }
        this.showSaveButton = true
        // this.actionButtonClicked = true;
      } else if (data && data.user_id == undefined) {

        if (editPartnerData == false) {
          let newStake = data.stakeHolder
          this.stakeHoldersList.push({ ...newStake });
          console.log("newStake", newStake)
          console.log("this.stakeHoldersList after new stake is", this.stakeHoldersList)
          let approveData = this.shouldShowApproveButtonInStake()
          console.log("approveData", approveData)
          if (newStake.ROLE == 'APPROVE') {
            let hideButton = this.hideConfirmButton(newStake.ROW_ID)
            console.log("hideButton" , hideButton)
            this.cdr.detectChanges();
          }
          if(newStake.ROLE == 'CLOSE'){
            // this.isHideNewStakeHolderButton = true;
            let hideButton = this.hideCloseButton(newStake.ROW_ID)
            this.checkIfCloseRoleInStakeHolders()
            console.log("hideButton" , hideButton)
            this.cdr.detectChanges();
          }
        }
      }

      // await this.createFormAndSetFormData();

    });

  }

  async save() {
    if(this.isFormFound){
     await this.saveForm();
    }
  
    if (this.permit.STATUS === 'CLOSED') {
      let permitformUpdateQuery = `UPDATE PERMIT_FORM SET COMPLETED = 'X', PARTIAL_FLAG = '' WHERE PERMIT_NO = '${this.permit.PERMIT_NO}'`;
      let permitformUpdateResult = await this.unviredSDK.dbExecuteStatement(
        permitformUpdateQuery
      );
      if (permitformUpdateResult.type == ResultType.success) {
        console.log("permitformUpdateResult" + permitformUpdateResult);
      } else {
        console.log("FAILURE - update permit form details in DB")
      }
    }

    switch (this.permit.STATUS) {
      case PermitStatus.OPEN:
        await this.savelog(PermitStatus.OPEN);
        this.modalController.dismiss(true);
        break;
      case PermitStatus.IN_REVIEW:
     
        if(this.stakeHoldersList.length != this.initialStakeholderlist.length || JSON.stringify(this.stakeHoldersList) != JSON.stringify(this.initialStakeholderlist) ){
          if (JSON.stringify(this.stakeHoldersList) != JSON.stringify(this.initialStakeholderlist)){
            
              const minLength = Math.min(this.stakeHoldersList.length, this.initialStakeholderlist.length);
            for (let i = 0; i < minLength; i++) {
              if (JSON.stringify(this.stakeHoldersList[i].USER_ID) != JSON.stringify(this.initialStakeholderlist[i].USER_ID)) {
                let inspectionLogUpdateQuery = `UPDATE PERMIT_STAKEHOLDER SET OBJECT_STATUS = 2, P_MODE = 'M' , USER_ID ='${this.stakeHoldersList[i].USER_ID}'  WHERE PERMIT_NO = '${this.stakeHoldersList[i].PERMIT_NO}' AND ROW_ID = '${this.stakeHoldersList[i].ROW_ID}'`;
                await this.unviredSDK.dbExecuteStatement(inspectionLogUpdateQuery);
              }
            }
            // if(this.stakeHoldersList.length == this.initialStakeholderlist.length){
            //   for(let i =0 ; i< this.stakeHoldersList.length ; i++){
            //     if(JSON.stringify(this.stakeHoldersList[i].USER_ID) != JSON.stringify(this.initialStakeholderlist[i].USER_ID)){
            //       let inspectionLogUpdateQuery = `UPDATE PERMIT_STAKEHOLDER SET OBJECT_STATUS = 2, P_MODE = 'M' , USER_ID ='${this.stakeHoldersList[i].USER_ID}'  WHERE PERMIT_NO = '${this.stakeHoldersList[i].PERMIT_NO}' AND ROW_ID = '${this.stakeHoldersList[i].ROW_ID}'`;
            //       await this.unviredSDK.dbExecuteStatement(inspectionLogUpdateQuery);
                  
            //     }
 
            //   }
            // }else
            
            if(this.stakeHoldersList.length != this.initialStakeholderlist.length){
                 for(let i = this.initialStakeholderlist.length ; i< this.stakeHoldersList.length ; i++){
                    let res = await this.unviredSDK.dbInsert('PERMIT_STAKEHOLDER' , this.stakeHoldersList[i] , false)
                  if (res.type === ResultType.success) {
                    let a = `SELECT * FROM PERMIT_STAKEHOLDER WHERE PERMIT_NO = '${this.permit.PERMIT_NO}'`;
                    let permitUpdateQueryResult = await this.unviredSDK.dbExecuteStatement(
                      a
                    );
                 
                  } else if(res.type == ResultType.error){
                    console.log("error in update stake", res.error)
                  }
                 }
              }
          }
        }

        //  let inspectionLogUpdateQuery = `UPDATE PERMIT_STAKEHOLDER SET OBJECT_STATUS = 1, P_MODE = 'M' , USER_ID ='${data.user_id}'  WHERE PERMIT_NO = '${stakeHolderData.PERMIT_NO}' AND ROW_ID = '${stakeHolderData.ROW_ID}'`;
        //    await this.unviredSDK.dbExecuteStatement(inspectionLogUpdateQuery);
        //   let getAddedPartner = `SELECT * FROM PERMIT_STAKEHOLDER WHERE USER_ID='${data.user_id}' AND PERMIT_NO = '${stakeHolderData.PERMIT_NO}' AND ROW_ID = '${stakeHolderData.ROW_ID}'`
        //   let res =   await this.unviredSDK.dbExecuteStatement(getAddedPartner);



        const hasRejectedStatusChange = this.stakeActionStatusArray.find(item =>
          item.status === "Rejected"
        );


        const updatePermitLog = async () => {


          for (let i = 0; i < this.stakeActionStatusArray.length; i++) {

            let stakeObj = this.stakeHoldersList.find((ele) => ele.ROW_ID == this.stakeActionStatusArray[i].ROW_ID)
            console.log("item that is rejected", this.stakeActionStatusArray[i])
            console.log("created new permit log")
            let permitLogObj = new PERMIT_LOG();
            permitLogObj.PERMIT_NO = stakeObj.PERMIT_NO;
            permitLogObj.OBJECT_STATUS = 1;
            permitLogObj.P_MODE = 'A';
            permitLogObj.FID = this.permit.LID;
            permitLogObj.APPR_TYPE = stakeObj.APPR_TYPE
            permitLogObj.LOG_NO = await this.findMaxLogNumberFromDb(
              this.permit.PERMIT_NO
            );

            if (stakeObj.ROLE == 'REVIEW' || stakeObj.ROLE == 'EXECUTE') {
              permitLogObj.ACTION = 'CONFIRM';
            } else {
              permitLogObj.ACTION = 'TRANSITION';
            }

            permitLogObj.CREATED_ON = new Date().getTime();


            permitLogObj.CREATED_BY = `${this.userObject.USER_ID}`;
            if (this.stakeActionStatusArray[i].status == 'Approved') {

              // this.permit.isRejected = true
              permitLogObj.PERMIT_STATUS = 'IN_REVIEW'
              permitLogObj.APPROVAL = 'A'
              this.permit.APPROVAL_SUMMARY = 'A'

            } else if (this.stakeActionStatusArray[i].status == 'Rejected') {

              permitLogObj.COMMENT = this.stakeActionStatusArray[i].comment

              // this.permit.isRejected = true
              permitLogObj.PERMIT_STATUS = 'IN_REVIEW'
              permitLogObj.APPROVAL = 'R'
              this.permit.APPROVAL_SUMMARY = 'R'


            } else if (this.stakeActionStatusArray[i].status == 'Executed') {
              permitLogObj.PERMIT_STATUS = 'IN_REVIEW';
              permitLogObj.APPROVAL = 'A'
            } else if (this.stakeActionStatusArray[i].status == 'Confirmed') {
              permitLogObj.PERMIT_STATUS = 'APPROVED';
              permitLogObj.APPROVAL = 'A'
            }

            console.log("the permit log obj inserted is", permitLogObj)
            let result = await this.unviredSDK.dbInsert('PERMIT_LOG', permitLogObj, false)
            if (result.type == ResultType.success) {

              this.rejectionCompleted = true;
            } else {
              console.error("Error while approving the action")
            }





          }
        }


        if (hasRejectedStatusChange) {

          const alert = await this.alertController.create({
            header: 'Save Changes?',
            message: 'The review has been rejected. Do you want to continue saving? Once saved, this action cannot be undone',
            buttons: [
              {
                text: 'No',
                role: 'cancel',
                cssClass: 'secondary',
                handler: (blah) => { },
              },
              {
                text: 'Yes',
                role: 'confirm',
                handler: async () => {

                  const rejectedItem = this.stakeHoldersList.find(item => item.actionButtonStatus === "Rejected");
                  await updatePermitLog();

                  //  let duplicateExists =  this.checkPermitLogDuplicate('IN_REVIEW')

                  //  if(duplicateExists){
                  //   this.unviredSDK.dbExportWebData()
                  //   console.log("duplicateExists")
                  //  }
                  await this.savelog(PermitStatus.APPROVED);
                  // this.dataService.showAlertMessage(
                  //   'Alert',
                  //   'Data saved'
                  // );
                  this.modalController.dismiss(true);

                }
              },
            ],
          });

          await alert.present();

        } else {
          console.log("updatePermitLog in 939")
          await updatePermitLog()

          await this.savelog(PermitStatus.APPROVED);
          this.modalController.dismiss(true);

        }




        break;
      case PermitStatus.APPROVED:
        const count1 = this.stakeHoldersList.filter(
          (item) => item.USER_ID === null || item.USER_ID === ''
        ).length;
        if (count1 == 0) {
          await this.generalupdatePermitLog(this.stakeActionStatusArray)
          await this.savelog(PermitStatus.ISSUED);
          this.modalController.dismiss(true);
        } else {
          this.dataService.showAlertMessage(
            'Alert',
            'Please add users for all the partners'
          );
        }
        break;
      case PermitStatus.ISSUED:
        const count2 = this.stakeHoldersList.filter(
          (item) => item.USER_ID === null || item.USER_ID === ''
        ).length;
        if (count2 == 0) {

          const closedCount = this.stakeActionStatusArray.filter(ele => ele.status === 'Closed').length;
const totalCloseStakeholders = this.stakeHoldersList.filter(s => s.ROLE === 'CLOSE').length;

if (
  this.stakeActionStatusArray.length > 0 &&
  closedCount === totalCloseStakeholders &&
  totalCloseStakeholders > 0
) {
  await this.generalupdatePermitLog(this.stakeActionStatusArray.filter(ele => ele.status === 'Closed'));
}

          // if(this.stakeActionStatusArray.length > 0 && this.stakeActionStatusArray.every(ele => ele.status == 'Closed')){
          //   await this.generalupdatePermitLog(this.stakeActionStatusArray)
          // }
        
          await this.savelog(PermitStatus.ISSUED);
          this.modalController.dismiss(true);
        } else {
          this.dataService.showAlertMessage(
            'Alert',
            'Please add users for all the partners'
          );
        }
        break;
    }
  }

  // async getUsersList() {
  //   let getUsersQuery = '';
  //   if (this.permit.AGENT_ID_EXT != null && this.permit.AGENT_ID_INT != null) {
  //     getUsersQuery = `select B.first_name,B.last_name,B.USER_ID,B.role_name,review,appr_type
  // from role_header as A join user_header as B on A.role_name=B.role_name
  //   JOIN USER_APPROVAL_TYPE_HEADER as C on B.USER_ID = C.USER_ID
  //   where B.User_id in (select user_id from agent_user_header where agent_id in ('${this.permit.AGENT_ID_EXT}','${this.permit.AGENT_ID_INT}'))  AND
  // 		review = 'true'`
  //   } else if (
  //     this.permit.AGENT_ID_EXT != null &&
  //     this.permit.AGENT_ID_INT == null
  //   ) {
  //     getUsersQuery = `select B.first_name,B.last_name,B.USER_ID,B.role_name,review,appr_type
  //     from role_header as A join user_header as B on A.role_name=B.role_name
  //       JOIN USER_APPROVAL_TYPE_HEADER as C on B.USER_ID = C.USER_ID
  //       where B.User_id in (select user_id from agent_user_header where agent_id in ('${this.permit.AGENT_ID_EXT}'))  AND
  //         review = 'true'`
  //   } else {
  //     getUsersQuery = `select B.first_name,B.last_name,B.USER_ID,B.role_name,review,appr_type
  //     from role_header as A join user_header as B on A.role_name=B.role_name
  //       JOIN USER_APPROVAL_TYPE_HEADER as C on B.USER_ID = C.USER_ID
  //       where B.User_id in (select user_id from agent_user_header where agent_id in ('${this.permit.AGENT_ID_INT}'))  AND
  //         review = 'true'`
  //   }

  //   let fetchgetUsersQueryResult = await this.unviredSDK.dbExecuteStatement(
  //     getUsersQuery
  //   );
  //   if (fetchgetUsersQueryResult?.data?.length > 0) {
  //     this.usersList = fetchgetUsersQueryResult?.data;
  //   }
  //   console.log('users list: ', this.usersList);
  // }

  isDissabledUserDropdown(stakeHolder: any) {

    let result = false;
    if (stakeHolder?.ROLE == PermitUserRole.REQUEST) {
      result = true;
    }

    // switch (this.permit?.STATUS) {
    //   case PermitStatus.IN_REVIEW:
    //     result = (stakeHolder?.USER_ID != this.userObject?.USER_ID || (stakeHolder?.ROLE == PermitUserRole.REQUEST || stakeHolder?.ROLE == PermitUserRole.ISSUE)) ? true : false;
    //     break;
    //   case PermitStatus.APPROVED:
    //     result =
    //       stakeHolder.ROLE == PermitUserRole.REQUEST ||
    //         stakeHolder.ROLE == PermitUserRole.REVIEW ||
    //         stakeHolder.ROLE == PermitUserRole.APPROVE ||
    //         stakeHolder.ROLE == PermitUserRole.EXECUTE ||
    //         stakeHolder.ROLE == PermitUserRole.CLOSE
    //         ? true
    //         : false;
    //     break;
    //   case PermitStatus.ISSUED:
    //     result =
    //       stakeHolder.ROLE == PermitUserRole.REQUEST ||
    //         stakeHolder.ROLE == PermitUserRole.REVIEW ||
    //         stakeHolder.ROLE == PermitUserRole.APPROVE ||
    //         stakeHolder.ROLE == PermitUserRole.EXECUTE ||
    //         stakeHolder.ROLE == PermitUserRole.ISSUE
    //         ? true
    //         : false;
    //     break;
    // }
    return result;
  }

  async savelog(status: string) {

    await this.loader.showBusyIndicator(this.translate.instant('Please wait') + '...', 'crescent');
    if (this.permitDocs?.length > 0) {
      // For browser
      if (this.dataService.getDevicePlatform() == 'browser') {
        await this.saveImagesForWeb(this.permitDocs);
        if (this.loader.isLoading) {
          await this.loader.dismissBusyIndicator();
        }
      } else {
        // For mobile devices
        // Save image in local file system (for mobile)
        for (let fi = 0; fi < this.permitDocs.length; fi++) {
          if (this.permitDocs[fi].file && this.permitDocs[fi].file != null) {
            let fileName = `image-${new Date()
              .getTime().toString(16)}.jpg`;

            let result: any = await this.attachmentService.writePicture(
              this.permitDocs[fi].file,
              fileName
            );
            if (result) {
              let id = this.unviredSDK.guid().replace(/-/g, '');
              let documentHeader = new DOCUMENT_HEADER();
              documentHeader.DOC_ID = id;
              documentHeader.FILE_NAME = fileName;
              documentHeader.TITLE = '';
              documentHeader.CREATED_BY = '';
              documentHeader.CREATED_ON = new Date().getTime();
              documentHeader.P_MODE = 'A';
              documentHeader.LID = this.unviredSDK.guid().replace(/-/g, '');
              // Fetch user id from user role table.
              let userResult = await this.unviredSDK.dbExecuteStatement(
                `SELECT * FROM USER_ROLE`
              );
              if (userResult.type == ResultType.success) {
                if (userResult.data && userResult.data.length > 0) {
                  documentHeader.CREATED_BY = userResult.data[0].USER_ID;
                }
              }

              await this.unviredSDK.dbInsertOrUpdate(
                'DOCUMENT_HEADER',
                documentHeader,
                true
              );

              await this.attachmentService.createAttachmentItem(
                id,
                result,
                this.permitDocs[fi].file,
                documentHeader.LID,
                fileName
              );

              // Save Permit doc

              let permitDoc = new PERMIT_DOC();
              permitDoc.PERMIT_NO = this.permit.PERMIT_NO;
              permitDoc.DOC_ID = id;
              permitDoc.DOC_TYPE = 'IMAGE';
              permitDoc.P_MODE = 'A';
              permitDoc.OBJECT_STATUS = 1;
              permitDoc.SYNC_STATUS = 0;
              permitDoc.FID = this.permit.LID;
              await this.unviredSDK.dbInsertOrUpdate(
                'PERMIT_DOC',
                permitDoc,
                false
              );
            }
          }
        }
      }
    }

    // Call Async modify document api
    if (this.dataService.getDevicePlatform() == 'browser') {

    } else {
      let fetchDocQuery = `SELECT * FROM PERMIT_DOC WHERE FID = '${this.permit.LID}'`;
      let fetchDocResult: any = await this.unviredSDK.dbExecuteStatement(
        fetchDocQuery
      );
      if (fetchDocResult && fetchDocResult.data.length > 0) {
        for (let doc = 0; doc < fetchDocResult.data.length; doc++) {
          let fetchDocHeaderQuery = `SELECT * FROM DOCUMENT_HEADER WHERE DOC_ID = '${fetchDocResult.data[doc].DOC_ID}'`;
          let fetchDocHeaderResult =
            await this.unviredSDK.dbExecuteStatement(fetchDocHeaderQuery);
          if (
            fetchDocHeaderResult &&
            fetchDocHeaderResult.data.length > 0
          ) {
            await this.attachmentService.uploadAttachment(
              fetchDocHeaderResult.data[0],
              status
            );
          }
        }
      }
    }
    // let PermitDocUpdateQuery = `UPDATE PERMIT_DOC WHERE OBJECT_STATUS = 1 AND P_MODE = 'A' AND PERMIT_NO = '${this.permit.PERMIT_NO}'`;
    // this.unviredSDK.dbUpdate('PERMIT_DOC', `OBJECT_STATUS = 1 AND P_MODE = 'A' AND PERMIT_NO = '${this.permit.PERMIT_NO}'`)
    // await this.unviredSDK.dbExecuteStatement(PermitDocUpdateQuery);
    let permitformQuery = `SELECT * FROM PERMIT_FORM WHERE PERMIT_NO = '${this.permit.PERMIT_NO}'`;
    let permitformResult = await this.unviredSDK.dbExecuteStatement(
      permitformQuery
    );
    if (permitformResult.type == ResultType.success) {
      console.log("permitformResult" + permitformResult);
      if (permitformResult?.data && permitformResult?.data?.length > 0 && permitformResult.data[0].DATA) {
        this.permit.COMMENTS = this.isloaded ? JSON.parse(permitformResult.data[0].DATA).permitComments : this.permit.COMMENTS;
        let permitUpdateQuery = `UPDATE PERMIT_HEADER SET COMMENTS = '${JSON.parse(permitformResult.data[0].DATA).permitComments}' WHERE PERMIT_NO = '${this.permit.PERMIT_NO}'`;
        let permitUpdateQueryResult = await this.unviredSDK.dbExecuteStatement(
          permitUpdateQuery
        );
      }
    } else {
      console.log("FAILURE - fetch permit form details from DB")
    }

    console.log("the permit stake before is" , this.stakeHoldersList)
    // this.unviredSDK.dbExportWebData();

    if (JSON.stringify(this.stakeHoldersList) != JSON.stringify(this.initialStakeholderlist)){
            if(this.stakeHoldersList.length == this.initialStakeholderlist.length){
              for(let i =0 ; i< this.stakeHoldersList.length ; i++){
                if(JSON.stringify(this.stakeHoldersList[i].USER_ID) != JSON.stringify(this.initialStakeholderlist[i].USER_ID)){
                  let inspectionLogUpdateQuery = `UPDATE PERMIT_STAKEHOLDER SET OBJECT_STATUS = 2, P_MODE = 'M' , USER_ID ='${this.stakeHoldersList[i].USER_ID}'  WHERE PERMIT_NO = '${this.stakeHoldersList[i].PERMIT_NO}' AND ROW_ID = '${this.stakeHoldersList[i].ROW_ID}'`;
                  await this.unviredSDK.dbExecuteStatement(inspectionLogUpdateQuery);
                  
                }
 
              }
            }
          }
    // let stakeHolderUpdateQuery = `UPDATE PERMIT_STAKEHOLDER SET OBJECT_STATUS = 2 WHERE PERMIT_NO = '${this.permit.PERMIT_NO}' AND P_MODE = 'M'`;
    // let stakeHolderUpdateQueryResult = await this.unviredSDK.dbExecuteStatement(
    //   stakeHolderUpdateQuery
    // );

    let getUpdatedStakeHolderquery = `select * from PERMIT_STAKEHOLDER WHERE PERMIT_NO = '${this.permit.PERMIT_NO}'  AND P_MODE = 'M'`;
    let getUpdatedStAKErESULT = await this.unviredSDK.dbExecuteStatement(
      getUpdatedStakeHolderquery
    )

    if (getUpdatedStAKErESULT.type == ResultType.success) {
      console.log("getUpdatedStAKErESULT" + getUpdatedStAKErESULT.data[0]);
    }


    let permitUpdateQuery = `UPDATE PERMIT_HEADER SET P_MODE = 'M', OBJECT_STATUS = 2 WHERE PERMIT_NO = '${this.permit.PERMIT_NO}'`;
    let permitUpdateQueryResult = await this.unviredSDK.dbExecuteStatement(
      permitUpdateQuery
    );
    if (permitUpdateQueryResult.type == ResultType.success) {
      this.permit.P_MODE = 'M';
      this.permit.OBJECT_STATUS = 2;
    }
    let isAvailableButton = this.permit.hasOwnProperty('isShowDetailsButton');
    if (isAvailableButton) {
      delete this.permit.isShowDetailsButton;
    }
    let isShowReviseButton = this.permit.hasOwnProperty('isShowReviseButton');
    if (isShowReviseButton) {
      delete this.permit.isShowReviseButton;
    }
    this.permit.EXTENSION_DATE = moment(this.extensionForm.controls['endDate'].value).valueOf();
    this.permit.IS_EXTENDED = (this.permit.IS_EXTENDED == true || this.permit.IS_EXTENDED == 'true') ? 'true' : 'false';
    if (this.permit.IS_EXTENDED == 'true') {
      // permitHolder.EXPIRY_DATE = moment(this.createForm.controls['endDate'].value).valueOf();
      let extensionDate = moment(this.extensionForm.controls['endDate'].value).valueOf()
      console.log("this.extensionForm.controls['endDate'].value", this.extensionForm.controls['endDate'].value)
      let updatePermitQuery = `UPDATE PERMIT_HEADER SET OBJECT_STATUS = 2 , P_MODE = 'M'  EXTENSION_DATE = '${extensionDate}' WHERE PERMIT_NO = '${this.permit.PERMIT_NO}'`;
      let permitUpdateQueryResult = await this.unviredSDK.dbExecuteStatement(
        updatePermitQuery
      );
    }

    // Remove display-only properties before sending to server
    if (this.permit.hasOwnProperty('isShowDetailsButton')) {
      delete this.permit.isShowDetailsButton;
    }
    if (this.permit.hasOwnProperty('isShowReviseButton')) {
      delete this.permit.isShowReviseButton;
    }
    // Remove permitTypeInfo as it's only used for display purposes and should not be sent to server
    if (this.permit.hasOwnProperty('permitTypeInfo')) {
      delete this.permit.permitTypeInfo;
    }

    let permitHeaderResponse: any = await this.dataService.modifyPermit(
      this.permit
    );
    let infoMsg = this.dataService.handleInfoMessage(permitHeaderResponse);
    if (permitHeaderResponse.type == ResultType.success) {
      if (infoMsg && infoMsg?.length > 0) {
        await this.showAlert('Info', infoMsg);
      } else {
        if (this.loader.isLoading) {
          await this.loader.dismissBusyIndicator();
        }
      }
    } else {
      if (
        permitHeaderResponse.message &&
        permitHeaderResponse.message.length > 0
      ) {
        await this.showAlert(
          this.translate.instant('Error'),
          permitHeaderResponse.message
        );
      } else if (
        permitHeaderResponse.error &&
        permitHeaderResponse.error.length > 0
      ) {
        await this.showAlert(
          this.translate.instant('Info'),
          permitHeaderResponse.error
        );
      } else {
        await this.showAlert(
          this.translate.instant('Error'),
          this.translate.instant(
            'Error occured while updating permit, Please try again'
          )
        );
      }
    }

  }

  async showAlert(title: string, message: string) {
    const alert = await this.alertController.create({
      header: title,
      message: message,
      buttons: [this.translate.instant('OK')],
    });
    await alert.present();
  }

  async saveImagesForWeb(images: any) {
    for (let i = 0; i < images.length; i++) {
      let isAvailable = images[i].hasOwnProperty('docItem');
      if (isAvailable) {
      } else {
        await this.attachmentService
          .uploadFileToServer(images[i].file)
          .then(async (result) => {
            console.log(result)

            console.log("result in upload", result)
            let documetAttachmentHeader = this.createDocumentAttachmentHeader(
              result.attachmentId,
              images[i].file
            );
            let documetHeader = await this.createDocumentHeader(
              result.attachmentId,
              images[i].file,
              'A'
            );

            let documetHeaderInput = {
              DOCUMENT: [
                {
                  DOCUMENT_HEADER: documetHeader,
                  DOCUMENT_ATTACHMENT: [documetAttachmentHeader],
                },
              ],
            };



            let updateDocumentResponse: any =
              await this.attachmentService.uploadAttachment(documetHeaderInput);


            let infoMsg = this.dataService.handleInfoMessage(updateDocumentResponse);
            if (updateDocumentResponse.type == ResultType.success) {
              this.loadingController.dismiss();
              if (infoMsg && infoMsg?.length > 0) {
                this.resultErrorMessage = infoMsg
                console.log("has infomessage")
              } else {
                this.modalController.dismiss(infoMsg);
                console.log("file uploaded in document")
              }
            } else {
              this.loadingController.dismiss();
              console.log("error file upload in document ")
              if (
                result.message &&
                result.message.length > 0
              ) {
                this.resultErrorMessage = result.message;
              } else if (
                result.error &&
                result.error.length > 0
              ) {
                this.resultErrorMessage = result.error
              } else {
                this.resultErrorMessage = 'Error occured while uploading doc, Please try again'
              }
            }


            if (updateDocumentResponse.type == ResultType.success) {
              console.log("now add doc in permit doc")
              // Save inspection doc
              let inspectionDoc = new PERMIT_DOC();
              inspectionDoc.PERMIT_NO = this.permit.PERMIT_NO;
              inspectionDoc.DOC_ID = result.attachmentId;
              inspectionDoc.DOC_TYPE = this.getFileExtension(images[i]?.file?.type);
              inspectionDoc.P_MODE = 'A';
              inspectionDoc.OBJECT_STATUS = 1;
              inspectionDoc.SYNC_STATUS = 0;
              inspectionDoc.FID = this.permit.LID;
              await this.unviredSDK.dbInsert(
                'PERMIT_DOC',
                inspectionDoc,
                false
              );
            }
          })
          .catch((error) => { });
      }
    }
  }

  createDocumentAttachmentHeader(attachmentUid: string, file: any): any {
    var documentAttachmentHeader = <DOCUMENT_ATTACHMENT>{};
    documentAttachmentHeader.UID = attachmentUid;
    documentAttachmentHeader.FILE_NAME = file.name;
    documentAttachmentHeader.MIME_TYPE = file.type;
    documentAttachmentHeader.ATTACHMENT_STATUS = 'UPLOADED';
    return documentAttachmentHeader;
  }

  // async createDocumentHeader(
  //   attachmentUid: string,
  //   file: any,
  //   mode: string
  // ): Promise<any> {
  //   var documentHeader = <DOCUMENT_HEADER>{};
  //   documentHeader.DOC_ID = attachmentUid;
  //   documentHeader.FILE_NAME = file.name;
  //   documentHeader.TITLE = '';
  //   documentHeader.CREATED_BY = '';
  //   documentHeader.CREATED_ON = moment().valueOf();
  //   documentHeader.P_MODE = mode;

  //   // Fetch user id from user role table.
  //   let userResult = await this.unviredSDK.dbExecuteStatement(
  //     `SELECT * FROM USER_ROLE`
  //   );
  //   if (userResult.type == ResultType.success) {
  //     if (userResult.data && userResult.data.length > 0) {
  //       documentHeader.CREATED_BY = userResult.data[0].USER_ID;
  //     }
  //   }
  //   return documentHeader;
  // }


  async createDocumentHeader(
    attachmentUid: string,
    file: any,
    mode: string
  ): Promise<any> {
    var documentHeader = <DOCUMENT_HEADER>{};
    documentHeader.DOC_ID = attachmentUid;
    documentHeader.FILE_NAME = file.name;

    documentHeader.TITLE = file.name;


    documentHeader.CREATED_BY = '';
    documentHeader.CREATED_ON = moment().valueOf();
    documentHeader.P_MODE = mode;
    documentHeader.MIME_TYPE = file.type;
    documentHeader.DOC_TYPE = this.getFileExtension(file?.type);


    // Fetch user id from user role table.
    let userResult = await this.unviredSDK.dbExecuteStatement(
      `SELECT * FROM USER_ROLE`
    );
    if (userResult.type == ResultType.success) {
      if (userResult.data && userResult.data.length > 0) {
        documentHeader.CREATED_BY = userResult.data[0].USER_ID;
      }
    }
    return documentHeader;
  }


  async getApproveCount() {
    let countQuery = `SELECT COUNT(ROLE) AS COUNT FROM PERMIT_STAKEHOLDER WHERE ROLE='APPROVE' AND PERMIT_NO= '${this.permit.PERMIT_NO}'`;
    let countResult = await this.unviredSDK.dbExecuteStatement(countQuery);
    if (countResult?.data[0]?.COUNT > 0) {
      return countResult?.data[0]?.COUNT;
    } else return 0;
  }

  async getExecuteCount() {
    let countQuery = `SELECT COUNT(ROLE) AS COUNT FROM PERMIT_STAKEHOLDER WHERE ROLE='EXECUTE' AND PERMIT_NO= '${this.permit.PERMIT_NO}'`;
    let countResult = await this.unviredSDK.dbExecuteStatement(countQuery);
    if (countResult?.data[0]?.COUNT > 0) {
      return countResult?.data[0]?.COUNT;
    } else return 0;
  }

  async getRoleCount(role: string) {
    let countQuery = `SELECT COUNT(ROLE) AS COUNT FROM PERMIT_STAKEHOLDER WHERE ROLE='${role}' AND PERMIT_NO= '${this.permit.PERMIT_NO}'`;
    let countResult = await this.unviredSDK.dbExecuteStatement(countQuery);
    if (countResult?.data[0]?.COUNT > 0) {
      return countResult?.data[0]?.COUNT;
    } else return 0;
  }

  // async checkPermitLogDuplicate(status: string) {
  //   let query = `SELECT * FROM PERMIT_LOG WHERE PERMIT_NO= '${this.permit.PERMIT_NO}' AND PERMIT_STATUS='${status}' AND P_MODE='A'`;
  //   let countResult = await this.unviredSDK.dbExecuteStatement(query);
  //   return countResult;
  // }

  async findMaxLogNumberFromDb(PERMIT_NO: any) {
    let maxNumber = 1;
    let fetchMaxLogNumberQuery = `SELECT MAX(LOG_NO) as maxNumber FROM PERMIT_LOG WHERE PERMIT_NO = '${PERMIT_NO}'`;
    let maxLogNumberResult: any = await this.unviredSDK.dbExecuteStatement(
      fetchMaxLogNumberQuery
    );
    if (maxLogNumberResult?.data[0]?.maxNumber) {
      maxNumber = maxLogNumberResult?.data[0]?.maxNumber + 1;
    }
    return maxNumber;
  }

  // async userChanged(event: any, stakeHolder: any) {
  //   console.log("user changed data " , stakeHolder , event)
  //   if (event?.detail?.value) {
  //     let userChangedQuery = '';
  //     if (stakeHolder.P_MODE == 'A') {
  //       userChangedQuery = `UPDATE PERMIT_STAKEHOLDER SET P_MODE = 'A, USER_ID = '${event?.detail?.value}', OBJECT_STATUS = 1 WHERE ROW_ID = '${stakeHolder.ROW_ID}' AND PERMIT_NO = '${stakeHolder.PERMIT_NO}' AND APPR_TYPE = '${stakeHolder.CONTEXT}' `;
  //     } else {
  //       userChangedQuery = `UPDATE PERMIT_STAKEHOLDER SET P_MODE = 'M', USER_ID = '${event?.detail?.value}', OBJECT_STATUS = 2 WHERE ROW_ID = '${stakeHolder.ROW_ID}' AND PERMIT_NO = '${stakeHolder.PERMIT_NO}' AND APPR_TYPE = '${stakeHolder.CONTEXT}' `;
  //     }
  //    await this.unviredSDK.dbExecuteStatement(userChangedQuery);
  //   }

  // }

  async deleteStakeHolder(stakeHolder: any, index: number) {
    const alert = await this.alertController.create({
      cssClass: 'my-custom-class',
      header: 'Confirm',
      backdropDismiss: false,
      message: `${this.translate.instant(
        'Are you sure you want to delete this partner?'
      )}`,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => { },
        },
        {
          text: 'Delete',
          handler: async () => {
            if (stakeHolder.P_MODE == 'A') {
              this.stakeHoldersList.splice(index, 1);
            } else {
              let inspectionLogUpdateQuery = `UPDATE PERMIT_STAKEHOLDER SET OBJECT_STATUS = 2, P_MODE = 'D' WHERE PERMIT_NO = '${this.permit.PERMIT_NO}' AND ROW_ID = '${stakeHolder.ROW_ID}'`;
              await this.unviredSDK.dbExecuteStatement(
                inspectionLogUpdateQuery
              );
              await this.getAllStakeHolders();
            }
          },
        },
      ],
    });
    await alert.present();
  }

  async setButtonType(val, calculatedPercentage) {
    let disableFormCompletion = this.attributesJson
      ? this.attributesJson.filter((e) => {
        return e.key.trim() == 'disable-form-completion';
      })
      : [];
    if (
      disableFormCompletion &&
      disableFormCompletion.length > 0 &&
      disableFormCompletion[0]
    ) {
      this.isdisableFormCompletion = disableFormCompletion[0].value;
    } else {
      this.isdisableFormCompletion = false;
    }
    console.log('isdisableFormCompletion ' + this.isdisableFormCompletion);

    // let calculatedPercentage = Math.round(Number(localStorage.getItem('eventPer')));
    calculatedPercentage = Math.round(Number(calculatedPercentage));
    // let val = await returnTempData();
    this.tempData = val.tempData ? val.tempData : val.submission;
    let completeFld: boolean = true;
    if (calculatedPercentage === 100) {
      let clientCompField = this.attributesJson
        ? this.attributesJson.filter((e) => {
          return e.key.trim() == 'client-complete-check-field';
        })
        : [];
      if (
        clientCompField.length > 0 &&
        this.tempData &&
        this.tempData.data &&
        clientCompField[0].value !== ''
      ) {
        completeFld = this.tempData.data[clientCompField[0].value];
      } else {
        if (clientCompField.length > 0 && clientCompField[0].value !== '') {
          completeFld = false;
        }
      }
    }
    // if (this.loader.isLoading && this.loader.message == this.translate.instant('Loading form...')) {
    //   await this.loader.dismissBusyIndicator();
    // }

    this.formReadOnlyFlag = false;
    if (
      !this.isdisableFormCompletion &&
      !this.formReadOnlyFlag &&
      calculatedPercentage === 100 &&
      completeFld
    ) {
      this.buttonType = this.translate.instant('Complete');
    } else if (
      (!this.isdisableFormCompletion &&
        ((!this.formReadOnlyFlag && calculatedPercentage !== 100) ||
          (calculatedPercentage === 100 && !completeFld))) ||
      (this.formReadWriteFlag && calculatedPercentage !== 100) ||
      calculatedPercentage !== 100 ||
      (this.formReadWriteFlag && calculatedPercentage === 100)
    ) {
      this.buttonType = this.translate.instant('Save');
    } else {
    }
  }



  async createFormAndSetFormData() {

    $('head').append(
      '<link rel="stylesheet" href="assets/js/formio/formio.full.min.css" type="text/css" />'
    );
    $('head').append(
      '<link rel="stylesheet" href="assets/css/responsive-grid.min.css" type="text/css" />'
    );
    await this.prepareFormResourcesAndSetFormData();
    this.subData = this.formSubmissionData;
    // let loggedInUserIds = await this.appSpecificUtility.getTeamsFromDB();
    this.formReadOnlyFlag = false;
    let loggedInUserIds = this.userObject.USER_ID
    this.FirstTimeFlag = this.shareData.getNavigateFromCameraPage();
    if (!this.FirstTimeFlag) {
      // Calling Creating form function.
      this.FirstTimeFlag = true;
      let userInfo: any = null;
      let company = this.dataService.getCompany();
      this.appSpecificUtility.getLoggedInUserInfo().subscribe((data) => {
        userInfo = data[0];
        if (
          userInfo &&
          Object.keys(userInfo).length === 0 &&
          userInfo.constructor === Object
        ) {
        } else {
          this.currentUserDetails = userInfo;
        }
      });
      this.isAndroid = this.platform.is('android');
      let iosPlatform = this.platform.is('ios');
      setTimeout(async () => {
        if (this.formData && this.formData.components) {
          let components = this.formData.components;
          for (let i = 0; i < components.length; i++) {
            if (
              components[i].type === 'form' &&
              components[i].tags &&
              components[i].tags.length > 0
            ) {
              for (let j = 0; j < components[i].tags.length; j++) {
                if (components[i].tags[j] === 'FormViewOnly') {
                  this.formViewOnlyFlag = true;
                } else if (components[i].tags[j] === 'FormReadWrite') {
                  this.formReadWriteFlag = true;
                }
              }
            }
          }

          if (!this.formReadWriteFlag) {
            this.formViewOnlyFlag = true;
          }
        }
        this.unviredSDK.logDebug(
          'PermitDetailsComponent',
          'ionViewDidEnter()',
          'submission data = ' + JSON.stringify(this.subData)
        );
        this.isFormLoaded = false;


        if (this.userRoleData) {
          this.loader.dismissBusyIndicator()
        }



        let userApprovalType = await this.dataService.getData(
          'USER_APPROVAL_TYPE'
        );
        let permitUserApprovalType = [];
        if (userApprovalType && userApprovalType.length > 0) {
          for (let i = 0; i < userApprovalType.length; i++) {
            permitUserApprovalType.push({
              APPR_TYPE: userApprovalType[i].APPR_TYPE,
            });
          }
        }
        let stakeholderDataForm;
        if (this.formReadOnlyFlag == false) {
          // window.form.stakeholderDataForm = this.selectedStakeholder
        }


        if (this.formData) {
          this.isFormFound = true;
          // this.formData[this.selectedStakeholder.APPR_TYPE].ApproverName= this.selectedStakeholder.USER_ID;
          let data = JSON.stringify(this.formData);
          data = data.replace(/@PERMIT_NO@/g, this.permit.PERMIT_NO);
          let getPermitDescriptionPipeResult: DbResult = await this.unviredSDK.dbExecuteStatement(`SELECT DESCRIPTION AS DESC FROM PERMIT_TYPE_HEADER WHERE PERMIT_TYPE='${this.permit.PERMIT_TYPE}'`);
          let description = getPermitDescriptionPipeResult?.data[0]?.DESC ? getPermitDescriptionPipeResult?.data[0]?.DESC : this.permit.PERMIT_TYPE;

          data = data.replace(/@PERMIT_TYPE@/g, description);
          data = data.replace(/@PERMIT_DESCRIPTION@/g, this.permit.DESCRIPTION);
          data = data.replace(/@PERMIT_STATUS@/g, this.permit.STATUS);
          const permitDate = new Date(this.permit.PERMIT_DATE);
          const options: any = {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
          }
          const expiryDate = new Date(this.permit.EXPIRY_DATE);
          data = data.replace(/@PERMIT_DATE@/g, permitDate.toLocaleString('en-US', options));
          data = data.replace(/@EXPIRY_DATE@/g, expiryDate.toLocaleString('en-US', options));
          data = data.replace(/@FACILITY_ID@/g, this.facilityName);
          let getPermitDivisionNamePipeResult: DbResult = await this.unviredSDK.dbExecuteStatement(`SELECT NAME AS NAME FROM DIVISION_HEADER WHERE DIVISION_ID='${this.permit.DIVISION_ID}'`);
          let division = getPermitDivisionNamePipeResult?.data[0]?.NAME ? getPermitDivisionNamePipeResult?.data[0]?.NAME : this.permit.DIVISION_ID;
          data = data.replace(/@DIVISION_ID@/g, division);
          data = data.replace(/@AGENT_ID_EXT@/g, this.permit.AGENT_ID_EXT ? this.permit.AGENT_ID_EXT : "");
          data = data.replace(/@AGENT_ID_INT@/g, this.permit.AGENT_ID_INT ? this.permit.AGENT_ID_INT : "");
          data = data.replace(/@REQUESTED_BY@/g, this.permit.REQUESTED_BY ? this.permit.REQUESTED_BY : "");
          let comments = this.permit.COMMENTS ? this.permit.COMMENTS : "";
          comments = comments.trim();
          data = data.replace(/@COMMENTS@/g, comments);
          if (this.selectedStakeholder != undefined && this.selectedStakeholder != null && this.formReadOnlyFlag == false) {
            data = data.replace(/@ROLE@/g, this.selectedStakeholder.ROLE);
            data = data.replace(/@APPR_TYPE@/g, this.selectedStakeholder.APPR_TYPE);

            // console.log(data)
            // data = data.replace(/@ApproverName@/g, this.selectedStakeholder.USER_ID);

            // if(this.actionButtonClicked){
            console.log("action button clicked")
            data = this.replaceApproverName(data, `${this.selectedStakeholder.APPR_TYPE}Name`, `${this.selectedStakeholder.USER_ID}`);
            let radioStatus
            if (this.stakeHolderActionStatus == 'Approved') {
              radioStatus = 'approve'
            } else if (this.stakeHolderActionStatus == 'Rejected') {
              radioStatus = 'reject'
            }
            console.log("radioStatus", radioStatus)
            data = this.replaceApproverRadio(data, `${this.selectedStakeholder.APPR_TYPE}Radio`, `${radioStatus}`);
            // }

            // console.log(data);


          }

          if (this.formReadOnlyFlag == false) {
            if (this.actionButtonClicked == true && this.segmentValue == 'form' && this.selectedStakeholder != null) {
              this.subData.RoleName = this.selectedStakeholder.ROLE
              this.subData.ApprovalType = this.selectedStakeholder.APPR_TYPE

              this.actionButtonClicked = false

            } else {
              this.subData.RoleName = 'null'
              this.subData.ApprovalType = 'null'
              window.form.stakeholderDataForm = 'null'
            }
          }



          this.formData = JSON.parse(data);
          if (this.formData && this.formData.components) {
            this.formData.components.forEach(component => {
              this.stakeHoldersList.forEach(stakeholder => {
                if (component.key === stakeholder.CONTEXT) {

                  this.showFillFormButton = true;

                } else {
                  this.showFillFormButton = false;
                }
              });
            });
          }



          console.log("window.stakeholderdata ", this.formReadOnlyFlag, window.form.stakeholderDataForm, this.selectedStakeholder)
          console.log('this.subData and formdata', this.subData, this.formData);
          this.subData.permitComments = comments;
          this.ngZone.run(async () => {
            loadForm(
              this.formData,
              this.subData,
              this.formReadOnlyFlag,
              this.translateJson,
              userInfo,
              company,
              this.isAndroid,
              iosPlatform,
              !this.isHybridNative,
              'form',
              this.attributesJson,
              loggedInUserIds,
              this.userRoleData,
              permitUserApprovalType,
              stakeholderDataForm
            );
          })
          // this.shareData.setFormData(data)
        } else {
          this.isFormFound = false;
        }
      }, 100);
      this.shareData.setFirstTimeFlag(true);
    } else {
      if (this.loader.isLoading) {
        await this.loader.dismissBusyIndicator();
      }
    }

    /**
     * Wait for Form to render and then add CSS to make the form responsive.
     * FIXME: Find a way to remove this eventlistener. Because, everytime this page is visited, eventlistener is getting added.
     */
    function formRenderingCompleteHandler(this: any, event) {
      this.unviredSDK.logDebug(
        'PermitDetailsComponent',
        'formRenderingCompleteHandler()',
        'Form Rendering is complete. Loading CSS..'
      );
      if (!this.isFormLoaded) {
        console.log('calling prepareTabulatorData');
        this.prepareTabulatorData();
        this.isFormLoaded = true;
      }
      this.unviredSDK.logDebug(
        'PermitDetailsComponent',
        'formRenderingCompleteHandler()',
        'Form Rendering is complete. Loading CSS Complete.'
      );
    }
    function formRenderingCompleteFromapprovalHandler(this: any, event) {
      console.log('formRenderingCompleteFromapprovalHandler called');
      this.prepareTabulatorData();
    }
    document.addEventListener(
      'FormRenderingComplete',
      formRenderingCompleteHandler.bind(this),
      false
    );

    document.addEventListener(
      'FormRenderingCompleteFromapproval',
      formRenderingCompleteFromapprovalHandler.bind(this),
      false
    );

    // set form documents flag
    // if(this.form && this.form.DOCUMENTS){
    // this.showFormDocs = (this.form.DOCUMENTS === '' || this.form.DOCUMENTS === null || this.form.DOCUMENTS === undefined) ? false : true;
    // }
    let component = this;
    setTimeout(async function () {
      document.addEventListener(
        'openBarcode',
        function (event: any) {
          component.componentObj = event.detail.compObj;
          component.setBarcode = true;
          component.componentID = event.detail.controlId;
        },
        false
      );
    }, 200);

    document.addEventListener(
      'smartDataEvent',
      async function (event: any) {
        const data = event.detail;
        component.subData =
          component.subData === '' ||
            component.subData === undefined ||
            component.subData === null
            ? {}
            : component.subData;
        component.subData = data;
        // component.taskSubmission.FORM_DATA = JSON.stringify(component.subData);
        component.formSubmissionData = JSON.stringify(component.subData);
      },
      false
    );

    let that = this;
    setTimeout(async function () {
      document.addEventListener(
        'openCamera',
        function (event: any) {
          that.cameraOpen = true;
          const dataToSend: NavigationExtras = {
            queryParams: { id: event.detail.controlId },
          };
          that.router.navigate(['camera'], dataToSend);
        },
        false
      );
    }, 200);
    document.addEventListener(
      'displaySDCWorkflowError',
      this.displaySDCWorkflowErrorHandler.bind(this),
      true
    );
  }
  displaySDCWorkflowErrorHandler(event: any) {
    this.loader.showWorkFlowToast(event.detail.errorMsg);
  }
  async prepareFormResourcesAndSetFormData() {
    // Get FORM DATA from last page |FORM, FORMSET|
    let data = this.shareData.getFormData();
    if (data) { } else {
      await this.getFormTempaltes();
      data = this.shareData.getFormData();
    }
    // On reload data would be undifined so take care of RELOAD scenario
    if (data === undefined || Object.keys(data).length == 0) {
      let FORM_IO_ENTITY = localStorage.getItem('FORM_IO_ENTITY');
      console.log("FORM_IO_ENTITY", FORM_IO_ENTITY)
      data =
        FORM_IO_ENTITY != '' && FORM_IO_ENTITY != undefined
          ? JSON.parse(FORM_IO_ENTITY)
          : '';
    }
    console.log('data in form -render', data);
    this.form = data.form;
    // this.formSubmissionData = submisson data from server

    if (this.form && this.form.FORM_TITLE) {
      this.formDescription = this.form.FORM_TITLE;
    }
    let formDesign = this.utilityFunction.decodeUnicode(
      this.form ? this.form.TEMPLATE : ''
    );
    let formIOComponent = formDesign === '' ? '' : JSON.parse(formDesign);
    // if (this.form && this.form.TRANSLATION) {
    //   this.translateJson = JSON.parse(this.utilityFunction.decodeUnicode(this.form? this.form.TRANSLATION:""));
    // }
    // if (this.form && this.form.ATTRIBUTES) {
    //   this.attributesJson = JSON.parse(this.form.ATTRIBUTES);
    // }

    // Get form resources depending on different platforms
    // FOR BROWSER
    if (!this.isHybridNative) {
      this.masterData = this.shareData.getMasterData();
    }

    await this.getNestedFormsDetails(formIOComponent);

    // var obj;
    // for (obj of nestedFormObj) {
    //   await this.getMasterdDataresource(obj.formDesign, 'dataSrc', 'masterdata');
    // }

    // Check file component in form components
    // JSON.stringify(formIOComponent, (_, nestedValue) => {
    //   if (nestedValue && nestedValue['type'] === 'file') {
    //     this.formFileCompArray.push(nestedValue.key);
    //     this.formFileCompObj.push(nestedValue)
    //   }
    //   return nestedValue;
    // });

    // this.fileUploadFlag = (this.formFileCompArray.length > 0) ? true : false;
    // if (this.form && this.form.RESOURCES && this.form.RESOURCES != "") {
    //   try {
    //     let FORM_IO_ENTITY = await this.getFormioMasterDataObjects(formIOComponent, 'dataSrc', 'json');
    //     if (FORM_IO_ENTITY != undefined && FORM_IO_ENTITY != '' && Object.keys(FORM_IO_ENTITY).length === 0) {
    //       this.formData = FORM_IO_ENTITY;
    //     } else {
    //       this.formData = formIOComponent;
    //     }
    //   } catch (error) {
    //     this.unviredSDK.logError('PermitDetailsComponent', 'getObjects()', 'ERROR: ' + error);
    //     if (this.loader.isLoading) {
    //       this.unviredSDK.logInfo('PermitDetailsComponent', 'ionViewDidEnter()', 'Hiding Busy indicator...')
    //       await this.loader.dismissBusyIndicator();
    //     }
    //   }
    // }

    // inject masterdata
    this.unviredSDK.logInfo(
      'PermitDetailsComponent',
      'prepareFormResourcesAndSetFormData()',
      'calling getMasterdDataresource()'
    );
    await this.getMasterdDataresource(formIOComponent, 'dataSrc', 'masterdata');
    this.formData = formIOComponent;
    this.shareData.setFormIOComponent(this.formSubmissionData, this.formData);
  }

  renderBtnClicked(type: string) {
    switch (type) {
      case this.translate.instant('Save'):
        this.markComplete = false;
        this.saveForm();
        break;
      case this.translate.instant('Complete'):
        this.completeFlagChange();
        break;
    }
  }

  async presentPopover(ev: any) {
    const optionPopover = await this.popoverCtrl.create({
      component: OptionsPopoverPage,
      event: ev,
      animated: true,
      showBackdrop: true,
      backdropDismiss: true,
      translucent: true,
      cssClass: 'renderOptions',
      componentProps: {
        optionType: 'renderer',
        readOnly: this.formReadOnlyFlag,
        form: this.form,
        formReadWriteFlag: this.formReadWriteFlag,
        percentageCompleted: this.calculatedPercentage,
        formData: this.tempData,
        isHybridNative: this.isHybridNative,
      },
    });
    await optionPopover.present();
    const { data } = await optionPopover.onDidDismiss();
    if (data !== undefined) {
      switch (data) {
        case this.translate.instant('Complete'):
          this.completeFlagChange();
          break;
        case this.translate.instant('Save'):
          this.markComplete = false;
          await this.saveForm();
          break;
      }
    }
  }
  async completeFlagChange() {
    let clientCompField = this.attributesJson
      ? this.attributesJson.filter((e) => {
        return e.key.trim() == 'client-complete-check-field';
      })
      : [];
    let completeFld =
      clientCompField.length > 0 &&
        this.tempData &&
        this.tempData.data !== '' &&
        clientCompField[0].value !== ''
        ? this.tempData.data[clientCompField[0].value]
        : true;
    this.markComplete = true;
    this.completeFlag = this.markComplete && completeFld;
    await this.saveForm();
  }

  async saveForm() {
    try {
      localStorage.setItem('renderBackFormDataChanged', 'true');
      if (!this.loader.isLoading) {
        await this.loader.showBusyIndicator(this.translate.instant('Saving Form...'), 'crescent');
      }

      console.log("the formdata display is ", this.formData)
      if (this.formData.display === 'wizard') {
        // FORM DATA TYPE WIZARD
        try {
          let val = await wizardSubmit();
          if (val.submission !== '') {
            let clientCompField = this.attributesJson
              ? this.attributesJson.filter((e) => {
                return e.key.trim() == 'client-complete-check-field';
              })
              : [];
            let completeFld: boolean = true;
            if (
              clientCompField.length > 0 &&
              val.submission &&
              val.submission.data &&
              clientCompField[0].value !== ''
            ) {
              completeFld = val.submission.data[clientCompField[0].value];
            }
            if (!completeFld && this.markComplete) {
              await this.confirmationAlert();
              // this.confirmationAlert(this.translate.instant('Form cannot be completed until all required fields are entered. Save instead?'));
            } else {
              let arr = [];
              let keys = Object.keys(val.submission.data);
              Object.values(val.submission.data).forEach((ele, i) => {
                if (ele === '') {
                  arr.push(keys[i]);
                }
              });
              let promptOnFormComplete: boolean = false;
              let promptOnFormCompleteData = this.attributesJson
                ? this.attributesJson.filter((e) => {
                  return e.key.trim() == 'prompt-on-form-complete';
                })
                : [];
              if (
                promptOnFormCompleteData &&
                promptOnFormCompleteData.length > 0 &&
                promptOnFormCompleteData[0]
              ) {
                promptOnFormComplete = promptOnFormCompleteData[0].value;
                if (promptOnFormComplete && this.markComplete) {
                  let msgFormComplete = this.attributesJson
                    ? this.attributesJson.filter((e) => {
                      return e.key.trim() == 'message-form-complete';
                    })
                    : [];
                  if (
                    msgFormComplete &&
                    msgFormComplete.length > 0 &&
                    msgFormComplete[0]
                  ) {
                    let res = await this.confirmationAlertForComplete(
                      msgFormComplete[0].value
                    );
                    console.log('res' + res);

                    if (res) {
                      if (!this.loader.isLoading) {
                        await this.loader.showBusyIndicator(
                          this.translate.instant('Completing Form...'),
                          'crescent'
                        );
                      }
                      await this.updateDataAndSendToServer(val.submission, 'M');
                    }
                  }
                } else {
                  if (!this.markComplete) {
                    await this.confirmationAlert();
                  } else {
                    await this.updateDataAndSendToServer(val.submission, 'M');
                  }
                }
              }
            }
          }
        } catch (err: any) {
          if (this.loader.isLoading) {
            this.unviredSDK.logInfo(
              'PermitDetailsComponent',
              'saveForm()',
              'Hiding Busy indicator...'
            );
            if (this.loader.isLoading) {
              await this.loader.dismissBusyIndicator();
            }
          }
          if (err.error !== '') {
            if (err.error === 'validation error') {
              if (this.completeFlag) {
                // if (this.taskUser.USER_ID === this.task.PRIMARY_USER) {
                // this.primaryUserValidationAlert(this.translate.instant('Please fill all mandatory fields to mark complete!'))
                // this.confirmationAlert(this.translate.instant('Please fill all mandatory fields to mark complete!'))
                // this.confirmationAlert(this.translate.instant('Form is partially filled. Still mark complete?'))
                await this.confirmationAlert();
                this.completeFlag = false;
                // } else {
                //   this.confirmationAlert(this.translate.instant('Form is not filled completely.  Proceed to mark complete?'));
                // }
              } else {
                await this.confirmationAlert();
                // this.confirmationAlert(this.translate.instant('Form cannot be completed until all required fields are entered. Save instead?'));
              }
            } else {
              let msg1 = '';
              if (err.error.length > 0) {
                for (var e11 = 0; e11 < err.error.length; e11++) {
                  msg1 = msg1 + '<br />' + err.error[e11].message;
                }
                msg1 = msg1.slice(6);
                this.loader.showToast(msg1);
              } else {
                this.loader.showToast(JSON.stringify(err.error));
              }
            }
          }
        }
      } else if (this.formData.display === 'form') {
        // FORM DATA TYPE IS FORM
        try {
          console.log("this.stakestatus", this.stakeHolderActionStatus, this.selectedStakeholder)
          let val = await submit();

          if (val.submission !== '') {
            let clientCompField = this.attributesJson
              ? this.attributesJson.filter((e) => {
                return e.key.trim() == 'client-complete-check-field';
              })
              : [];
            let completeFld: boolean = true;
            if (
              clientCompField.length > 0 &&
              val.submission &&
              val.submission.data &&
              clientCompField[0].value !== ''
            ) {
              completeFld = val.submission.data[clientCompField[0].value];
            }
            if (!completeFld && this.markComplete) {
              await this.confirmationAlert();
              // this.confirmationAlert(this.translate.instant('Form cannot be completed until all required fields are entered. Save instead?'));
            } else {
              let arr = [];
              let keys = Object.keys(val.submission.data);
              Object.values(val.submission.data).forEach((ele, i) => {
                if (ele === '') {
                  arr.push(keys[i]);
                }
              });
              let promptOnFormComplete: boolean = false;
              let promptOnFormCompleteData = this.attributesJson
                ? this.attributesJson.filter((e) => {
                  return e.key.trim() == 'prompt-on-form-complete';
                })
                : [];
              if (
                promptOnFormCompleteData &&
                promptOnFormCompleteData.length > 0 &&
                promptOnFormCompleteData[0]
              ) {
                promptOnFormComplete = promptOnFormCompleteData[0].value;
                if (promptOnFormComplete && this.markComplete) {
                  let msgFormComplete = this.attributesJson
                    ? this.attributesJson.filter((e) => {
                      return e.key.trim() == 'message-form-complete';
                    })
                    : [];
                  if (
                    msgFormComplete &&
                    msgFormComplete.length > 0 &&
                    msgFormComplete[0]
                  ) {
                    let res = await this.confirmationAlertForComplete(
                      msgFormComplete[0].value
                    );
                    console.log('res' + res);
                    if (res) {
                      if (!this.loader.isLoading) {
                        await this.loader.showBusyIndicator(
                          this.translate.instant('Completing Form...'),
                          'crescent'
                        );
                      }
                      await this.updateDataAndSendToServer(val.submission, 'M');
                    }
                  }
                } else {
                  if (!this.markComplete) {
                    await this.confirmationAlert();
                  } else {
                    console.log('updateDataAndSendToServer at line 1173');
                    await this.updateDataAndSendToServer(val.submission, 'M');
                  }
                }
              } else {
                if (!this.markComplete) {
                  await this.confirmationAlert();
                } else {
                  console.log('updateDataAndSendToServer at line 1181');
                  await this.updateDataAndSendToServer(val.submission, 'M');
                }
              }
            }
          }
        } catch (err: any) {
          if (err.error !== '') {
            if (err.error === 'validation error') {

              let components = this.formData.components;
              let isApprovalform = false;


              for (let i = 0; i < components.length; i++) {

                if (
                  components[i].type === 'form' &&
                  components[i].tags &&
                  components[i].tags.length > 0
                ) {

                  for (let j = 0; j < components[i].tags.length; j++) {
                    if (components[i].tags[j] === 'Approval') {
                      isApprovalform = true;
                    }
                  }
                }
              }
              if (this.completeFlag) {
                this.completeFlag = false;
                await this.confirmationAlert();
              } else if (isApprovalform) {
                isApprovalform = false;
                await this.confirmationAlert();
              } else {
                await this.confirmationAlert();
              }
            } else {
              let msg = '';
              if (err.error.length > 0) {
                for (var e1 = 0; e1 < err.error.length; e1++) {
                  msg = msg + '<br />' + err.error[e1].message;
                }
                msg = msg.slice(6);
                this.loader.showToast(msg);
              } else {
                this.loader.showToast(JSON.stringify(err.error));
              }
              if (this.loader.isLoading) {
                this.unviredSDK.logInfo(
                  'PermitDetailsComponent',
                  'saveForm()',
                  'Hiding Busy indicator...'
                );
                if (this.loader.isLoading) {
                  await this.loader.dismissBusyIndicator();
                }
              }
            }
          }
        }
      } else {
        // other types of forms
        try {
          let val = await submit();
          if (val.submission !== '') {
            let clientCompField = this.attributesJson
              ? this.attributesJson.filter((e) => {
                return e.key.trim() == 'client-complete-check-field';
              })
              : [];
            let completeFld: boolean = true;
            if (
              clientCompField.length > 0 &&
              val.submission &&
              val.submission.data &&
              clientCompField[0].value !== ''
            ) {
              completeFld = val.submission.data[clientCompField[0].value];
            }
            if (!completeFld) {
              await this.confirmationAlert();
            } else {
              let arr = [];
              let keys = Object.keys(val.submission.data);
              Object.values(val.submission.data).forEach((ele, i) => {
                if (ele === '') {
                  arr.push(keys[i]);
                }
              });
              await this.updateDataAndSendToServer(val.submission, 'M');
            }
          }
        } catch (err: any) {
          if (this.loader.isLoading) {
            this.unviredSDK.logInfo(
              'PermitDetailsComponent',
              'saveForm()',
              'Hiding Busy indicator...'
            );
            if (this.loader.isLoading) {
              await this.loader.dismissBusyIndicator();
            }
          }
          if (err.error !== '') {
            if (err.error === 'validation error') {
              if (this.completeFlag) {
                await this.confirmationAlert();
                this.completeFlag = false;
              } else {
                await this.confirmationAlert();
              }
            } else {
              this.loader.showToast(err.error);
            }
          }
        }
      }
    } catch (error) {
      if (this.loader.isLoading) {
        await this.loader.dismissBusyIndicator();
      }
    }
  }
  async confirmationAlert() {
    // Partial Save event
    await this.getPartialFilledData();

    if (this.markComplete) {
      let messages = this.translate.instant(
        'Form cannot be completed until all required fields are entered. Save instead?'
      );
      const alert = await this.alertController.create({
        header: this.translate.instant('Confirmation'),
        message: messages,
        animated: true,
        backdropDismiss: false,
        buttons: [
          {
            text: this.translate.instant('No'),
            role: 'cancel',
            handler: () => { },
          },
          {
            text: this.translate.instant('Yes'),
            handler: async () => {
              await this.draftForm('yes');
            },
          },
        ],
      });
      if (this.loader.isLoading) {
        this.unviredSDK.logInfo(
          'PermitDetailsComponent',
          'confirmationAlert()',
          'Hiding Busy indicator...'
        );
        if (this.loader.isLoading) {
          await this.loader.dismissBusyIndicator();
        }
      }
      await alert.present();
    } else {
      // Save
      // show promt when prompt-on-form-save is configured else save form
      let promptOnFormSave: boolean = false;
      let msg = '';
      if (!this.markComplete) {
        let promptOnFormSaveData = this.attributesJson
          ? this.attributesJson.filter((e) => {
            return e.key.trim() == 'prompt-on-form-save';
          })
          : [];
        if (
          promptOnFormSaveData &&
          promptOnFormSaveData.length > 0 &&
          promptOnFormSaveData[0]
        ) {
          promptOnFormSave = promptOnFormSaveData[0].value;
          if (promptOnFormSave) {
            let msgFormSave = this.attributesJson
              ? this.attributesJson.filter((e) => {
                return e.key.trim() == 'message-form-save';
              })
              : [];
            if (msgFormSave && msgFormSave.length > 0 && msgFormSave[0]) {
              msg = msgFormSave[0].value;
            }
          }
        }
      }
      if (promptOnFormSave) {
        let messages = '';
        if (msg && msg !== '' && msg.length > 0) {
          messages = msg;
        }
        if (this.calculatedPercentage === 100) {
          await this.draftForm('yes');
        } else {
          const alert = await this.alertController.create({
            header: this.translate.instant('Confirmation'),
            message: messages,
            animated: true,
            backdropDismiss: false,
            buttons: [
              {
                text: this.translate.instant('No'),
                role: 'cancel',
                handler: () => { },
              },
              {
                text: this.translate.instant('Yes'),
                handler: async () => {
                  await this.draftForm('yes');
                },
              },
            ],
          });
          if (this.loader.isLoading) {
            this.unviredSDK.logInfo(
              'PermitDetailsComponent',
              'confirmationAlert()',
              'Hiding Busy indicator...'
            );
            if (this.loader.isLoading) {
              await this.loader.dismissBusyIndicator();
            }
          }
          await alert.present();
        }
      } else {
        console.log("this.selectedstakeholder", this.selectedStakeholder, this.stakeHolderActionStatus)


        await this.draftForm('yes');
      }
    }
  }
  async draftForm(mode: any) {
    try {
      if (!this.loader.isLoading) {
        await this.loader.showBusyIndicator(
          this.translate.instant('Saving Form...'),
          'crescent'
        );
      }
      let val = await returnTempData();
      if (mode === 'yes') {
        let tempData = val.tempData ? val.tempData : val.submission;
        await this.updateDataAndSendToServer(tempData, 'A');
      } else {
        if (val.tempData !== '') {
          this.updateDataOnDraftForm(val.tempData);
        } else {
          let clientCompField = this.attributesJson
            ? this.attributesJson.filter((e) => {
              return e.key.trim() == 'client-complete-check-field';
            })
            : [];
          let completeFld =
            clientCompField.length > 0 &&
              val.submission &&
              val.submission.data !== '' &&
              clientCompField[0].value !== ''
              ? val.submission.data[clientCompField[0].value]
              : true;
          if (!completeFld) {
            this.updateDataOnDraftForm(val.submission);
          }
        }
      }
    } catch (error) {
      if (this.loader.isLoading) {
        this.unviredSDK.logInfo(
          'PermitDetailsComponent',
          'draftForm()',
          'Hiding Busy indicator...'
        );
        if (this.loader.isLoading) {
          await this.loader.dismissBusyIndicator();
        }
      }
      this.unviredSDK.logError(
        'PermitDetailsComponent',
        'draftForm()',
        'ERROR: ' + error
      );
    }
  }
  async updateDataOnDraftForm(data: any) {
    await this.updateDB(data);
    await this.unviredSDK.unlockDataSender();
    if (this.loader.isLoading) {
      this.unviredSDK.logInfo(
        'PermitDetailsComponent',
        'draftForm()',
        'Hiding Busy indicator...'
      );
      if (this.loader.isLoading) {
        await this.loader.dismissBusyIndicator();
      }
    }
    // }
  }

  async updateDB(submission: any) {
    console.log("update db called")
    let dbResponseForTaskSubmission = {} as DbResult;
    try {
      // this.permitFormSubmissionData.DATA = JSON.stringify(submission.data);
      try {
        let permitform: DbResult = await this.unviredSDK.dbExecuteStatement(`SELECT * FROM PERMIT_FORM WHERE PERMIT_NO = '${this.permitHeaderData.PERMIT_NO}' AND FORM_ID = '${this.form.FORM_ID}'`);
        console.log("permitform" + permitform)
        if (permitform.type === ResultType.success) {
          if (permitform?.data?.length > 0) {
            let permitFormDBRes = permitform.data[0]
            this.permitHeaderData.COMMENTS = (submission.data) ? submission.data.permitComments : this.permitHeaderData.COMMENTS;
            permitFormDBRes.DATA = JSON.stringify(submission.data);
            if (this.markComplete) {
              permitFormDBRes.PARTIAL_FLAG = AppConstants.NO;
              permitFormDBRes.COMPLETED = AppConstants.YES;
            } else {
              permitFormDBRes.PARTIAL_FLAG = AppConstants.YES;
              permitFormDBRes.COMPLETED = AppConstants.NO;
            }

            permitFormDBRes.CHANGED_ON = moment().valueOf();
            permitFormDBRes.P_MODE = 'M';
            permitFormDBRes.OBJECT_STATUS = 2;
            permitFormDBRes.FID = this.permit.LID;
            dbResponseForTaskSubmission = await this.unviredSDK.dbInsertOrUpdate('PERMIT_FORM', permitFormDBRes, false);
            if (dbResponseForTaskSubmission.type === ResultType.success) {
              let a = `SELECT * FROM PERMIT_FORM WHERE PERMIT_NO = '${this.permit.PERMIT_NO}'`;
              let permitUpdateQueryResult = await this.unviredSDK.dbExecuteStatement(
                a
              );
              // console.log("permit form after updated " + JSON.stringify(permitUpdateQueryResult));
            }
            await this.showResponseWithToaster(
              dbResponseForTaskSubmission,
              this.loader.isLoading
            );

            // Update task_submission properties
            // this.permitFormSubmissionData.PERMIT_NO =
            //   this.permitHeaderData.PERMIT_NO;
            // this.permitFormSubmissionData.FORM_NAME = this.form.FORM_NAME;
            // this.permitFormSubmissionData.FORM_TITLE = this.form.FORM_TITLE;
            // this.permitFormSubmissionData.FORM_VERSION = this.form.FORM_VERSION;
            // this.permitFormSubmissionData.FORM_ID = this.form.FORM_ID;

            // this.permitFormSubmissionData.FORM_GUID = this.permitFormSubmissionData.FORM_GUID? this.permitFormSubmissionData.FORM_GUID : this.unviredSDK
            //   .guid()
            //   .replace(/-/g, '');
            // // this.permitFormSubmissionData.OBJECT_STATUS = this.constants.OBJECT_STATUS.MODIFY;
            // this.permitFormSubmissionData.LID = this.permitFormSubmissionData.LID
            //   ? this.permitFormSubmissionData.LID
            //   : this.utilityFunction.generateUUID();
            // // this.permitFormSubmissionData.OBJECT_STATUS = this.constants.OBJECT_STATUS.ADD;
            // this.permitFormSubmissionData.FID = this.permitHeaderData.LID
            //   ? this.permitHeaderData.LID
            //   : '';
            // if (this.markComplete) {
            //   this.permitFormSubmissionData.PARTIAL_FLAG = AppConstants.NO;
            //   this.permitFormSubmissionData.COMPLETED = AppConstants.YES;
            // } else {
            //   this.permitFormSubmissionData.PARTIAL_FLAG = AppConstants.YES;
            //   this.permitFormSubmissionData.COMPLETED = AppConstants.NO;
            // }
            // this.permitFormSubmissionData.CHANGED_ON = moment().valueOf();
            // let userContext: any = localStorage.getItem('userContext');
            // let user = '';
            // if (userContext) {
            //   userContext = JSON.parse(userContext);
            // }

            // if (userContext?.USER_CONTEXT_HEADER) {
            //   user =
            //     userContext?.USER_CONTEXT_HEADER?.FIRST_NAME +
            //     ' ' +
            //     userContext?.USER_CONTEXT_HEADER?.LAST_NAME;
            // }
            // this.permitFormSubmissionData.CHANGED_BY = user;

            // this.permitFormSubmissionData.OBJECT_STATUS = 2;
            // this.permitFormSubmissionData.P_MODE = pMode;
            // let res = await this.checkPermitFormDuplicate(this.form.FORM_ID);
            // if (res && res.data.length > 0) {
            //   this.permitFormSubmissionData.P_MODE = 'M';
            //   this.permitFormSubmissionData.OBJECT_STATUS = 2;
            //   let deleteQuery = `DELETE FROM PERMIT_FORM WHERE PERMIT_NO = '${this.permit.PERMIT_NO}' AND FORM_ID = '${this.form.FORM_ID}'`;
            //   await this.unviredSDK.dbExecuteStatement(deleteQuery);
            // }else{
            //   this.permitFormSubmissionData.P_MODE = 'A';
            //   this.permitFormSubmissionData.OBJECT_STATUS = 2;
            // }
            // dbResponseForTaskSubmission = await this.unviredSDK.dbInsert('PERMIT_FORM', this.permitFormSubmissionData, false);
            // let permitStakeHolderUpdateQuery = `UPDATE PERMIT_FORM SET OBJECT_STATUS = 2,CHANGED_BY = '${user}', P_MODE='M', PARTIAL_FLAG='X', COMPLETED='', FORM_GUID = '${this.unviredSDK
            //   .guid()
            //   .replace(/-/g, '')}' WHERE PERMIT_NO = '${
            //   this.permitFormSubmissionData.PERMIT_NO
            // }' AND FORM_ID = '${this.permitFormSubmissionData.FORM_ID}'`;
            // let res = await this.unviredSDK.dbExecuteStatement(
            //   permitStakeHolderUpdateQuery
            // );

            // dbResponseForTaskSubmission = await this.dataService.updatePermiform(this.permitFormSubmissionData, this.permitFormSubmissionData.PERMIT_NO, this.permitFormSubmissionData.FORM_ID)
            // dbResponseForTaskSubmission = await this.unviredSDK.dbUpdate(this.constants.PERMIT_FORM, this.permitFormSubmissionData, `PERMIT_NO = '${this.permitFormSubmissionData.PERMIT_NO}' AND FORM_ID = '${this.permitFormSubmissionData.FORM_ID}'`);
            // await this.showResponseWithToaster(
            //   dbResponseForTaskSubmission,
            //   this.loader.isLoading
            // );

          } else {
            // zero records found
          }
        } else {
          // error resp
        }
      } catch (error) {
        this.unviredSDK.logError(
          'PermitDetailsComponent',
          'updateDB()',
          'Caught Error: ' + JSON.stringify(error)
        );
        if (this.loader.isLoading) {
          this.unviredSDK.logInfo(
            'PermitDetailsComponent',
            'updateDB()',
            'Hiding Busy indicator...'
          );
          if (this.loader.isLoading) {
            await this.loader.dismissBusyIndicator();
          }
        }
        await this.loader.showAlert(
          'Error',
          this.translate.instant('Record not inserted!')
        );
      }
    } catch (error: any) {
      this.unviredSDK.logError(
        'PermitDetailsComponent',
        'updateDB()',
        'Caught Error: ' + JSON.stringify(error)
      );
      if (this.loader.isLoading) {
        this.unviredSDK.logInfo(
          'PermitDetailsComponent',
          'updateDB()',
          'Hiding Busy indicator...'
        );
        if (this.loader.isLoading) {
          await this.loader.dismissBusyIndicator();
        }
      }
      await this.loader.showAlert('Error', error);
    }
  }
  async getPartialFilledData() {
    // Get form data when form filled partially
    try {
      let partialFilledData = await returnTempData();
      let arr = [];
      let keys = Object.keys(partialFilledData.tempData.data);
      Object.values(partialFilledData.tempData.data).forEach((ele, i) => {
        if (ele === '') {
          arr.push(keys[i]);
        }
      });
    } catch (error) {
      this.unviredSDK.logError(
        'PermitDetailsComponent',
        'getPartialFilledData()',
        'ERROR: ' + error
      );
    }
  }

  async confirmationAlertForComplete(msg: string): Promise<boolean> {
    let resolveFunction: (confirm: boolean) => void;
    let promise = new Promise<boolean>((resolve) => {
      resolveFunction = resolve;
    });

    const alert = await this.alertController.create({
      header: this.translate.instant('Confirmation'),
      message: msg,
      animated: true,
      backdropDismiss: false,
      buttons: [
        {
          text: this.translate.instant('No'),
          role: 'cancel',
          handler: () => {
            resolveFunction(false);
          },
        },
        {
          text: this.translate.instant('Yes'),
          handler: () => {
            resolveFunction(true);
          },
        },
      ],
    });
    if (this.loader.isLoading) {
      this.unviredSDK.logInfo(
        'PermitDetailsComponent',
        'confirmationAlertForComplete()',
        'Hiding Busy indicator...'
      );
      if (this.loader.isLoading) {
        await this.loader.dismissBusyIndicator();
      }
    }
    await alert.present();
    return promise;
  }

  async updateDataAndSendToServer(formSubmissionData: any, pMode: string) {
    try {
      let selectedPermit = localStorage.getItem('selectedPermit');

      let permit;
      if (selectedPermit) {
        permit = JSON.parse(selectedPermit);
      }
      this.permitHeaderData = permit;
      let fetchTaskSubmission: DbResult = await this.dataService.getFormDetails(
        permit.PERMIT_NO,
        this.form.FORM_ID
      );

      if (fetchTaskSubmission.type === ResultType.success) {
        let latestSubmissionDB: PERMIT_FORM[] = fetchTaskSubmission.data;
        let latestSubmission: PERMIT_FORM = latestSubmissionDB[0];
        let latestSubmissionTimestamp: number;
        console.log("formSubmissionData ", formSubmissionData, latestSubmissionDB[0])
        await this.goBackToLastLocation(formSubmissionData)

        await this.unviredSDK.unlockDataSender();
        if (this.loader.isLoading) {
          this.unviredSDK.logInfo(
            'PermitDetailsComponent',
            'updateDataAndSendToServer()',
            'Hiding Busy indicator...'
          );
          if (this.loader.isLoading) {
            await this.loader.dismissBusyIndicator();
          }
        }
      } else {
        await this.loader.showToast(
          this.translate.instant('Missing submission! try again later.')
        );
      }
    } catch (error) {
      this.unviredSDK.logError(
        'PermitDetailsComponent',
        'updateDataAndSendToServer()',
        'ERROR: ' + error
      );
    }
  }

  async serverResponseHandler(response: SyncResult, busyindicator?: boolean) {
    if (response.code && response.code === 401) {
      if (busyindicator) {
        this.unviredSDK.logInfo(
          'PermitDetailsComponent',
          'serverResponseHandler()',
          'Hiding Busy indicator...'
        );
        if (this.loader.isLoading) {
          await this.loader.dismissBusyIndicator();
        }
      }
      this.appSpecificUtility.performLogoutOperation();
    } else {
      switch (response.type) {
        case ResultType.success:
          await this.showResponseWithToaster(response, busyindicator);
          break;
        case ResultType.error:
          await this.showResponseWithToaster(response, busyindicator);
          break;
      }
    }
  }

  async showResponseWithToaster(response: any, busyindicator?: boolean) {
    if (response.message && response.message.trim().length !== 0) {
      if (busyindicator) {
        this.unviredSDK.logInfo(
          'PermitDetailsComponent',
          'showResponseWithToaster()',
          'Hiding Busy indicator...'
        );
        if (this.loader.isLoading) {
          await this.loader.dismissBusyIndicator();
        }
      }
      this.loader.showToast(response.message);
    } else if (response.error && response.error.trim().length !== 0) {
      if (busyindicator) {
        this.unviredSDK.logInfo(
          'PermitDetailsComponent',
          'showResponseWithToaster()',
          'Hiding Busy indicator...'
        );
        if (this.loader.isLoading) {
          await this.loader.dismissBusyIndicator();
        }
      }
      // this.toastr.error('', response.error, { timeOut: 2000 });
    }
  }
  destroyScannerComponent() {
    this.setBarcode = !this.setBarcode;
    this.cameraOpen = !this.cameraOpen;
    this.shareData.getBackToRenderer.next('');
  }

  async getNestedFormsDetails(obj: any) {
    var objects = [];
    for (var i in obj) {
      if (typeof obj[i] == 'object') {
        objects = objects.concat(this.getNestedFormsDetails(obj[i]));
      } else if (
        i == 'nestedFormComponenttype' &&
        obj['nestedFormComponenttype'] == 'smartNestedForm'
      ) {
        let selNestedFormVersion = obj.selNestedFormVersion;
        let selectedNestedFormId = obj.selectedNestedFormId;

        if (
          selNestedFormVersion != undefined &&
          selNestedFormVersion != '' &&
          selectedNestedFormId != undefined &&
          selectedNestedFormId != ''
        ) {
          let result = await this.dataService.getNestedformDetailsFromDB(
            selNestedFormVersion,
            selectedNestedFormId
          );
          if (result && result.type === ResultType.success) {
            if (result['data']) {
              obj.formDesign = JSON.parse(result['data']);
              await this.getMasterdDataresource(
                obj.formDesign,
                'dataSrc',
                'masterdata'
              );
            }
          }
        }
      }
    }
    return true;
  }
  async getMasterdDataresource(obj: any, key: any, val: any) {
    var objects = [];

    for (var i in obj) {
      if (typeof obj[i] == 'object') {
        objects = objects.concat(
          await this.getMasterdDataresource(obj[i], key, val)
        );
      } else if (i == key && obj[key] == val) {
        var val1 = obj['valueProperty'];
        var ress;
        if (obj.data && typeof obj.data == 'object' && obj.data != undefined) {
          if (obj.data['masterdata']) {
            ress = obj.data['masterdata'];
          }
        }
        var obj1 = [];

        if (ress != undefined && ress != '') {
          // let masterdataResourcequery = `SELECT * FROM ${this.constants.MASTER_DATA_TABLE} WHERE RESOURCE_NAME = '${ress}'`;
          // let masterdataResults: DbResult = await this.unviredSDK.dbExecuteStatement(masterdataResourcequery);
          // if (masterdataResults.type == 0 && masterdataResults.data) {
          //   if (masterdataResults.data.length > 0) {
          //     for (let i = 0; i < masterdataResults.data.length; i++) {
          //       if (masterdataResults.data[i].DATA) {
          //         obj1.push(JSON.parse(masterdataResults.data[i].DATA));
          //       }
          //     }
          //   } else {
          //   }
          // }
        } else {
          this.unviredSDK.logInfo(
            'PermitDetailsComponent',
            'getMasterdDataresource()',
            'selected masterdata resource is empty'
          );
          obj1 = [];
        }
        var tmplt = obj['template'];
        if (tmplt != '') {
          var str1 = tmplt.substring(tmplt.lastIndexOf('.') + 1, tmplt.length);
          var str2 = str1.substring(0, str1.indexOf('}'), str1.length);
          var str3 = '<span>{{' + 'item.' + str2.trim() + '}}</span>';
          obj['template'] = str3;
        }
        var val = obj['valueProperty'];
        var valProp = '';
        if (val != '') {
          valProp = val.substring(val.lastIndexOf('.') + 1, val.length);
          obj['valueProperty'] = valProp;
        }
        str2 = str2.trim();
        obj1 = obj1.sort(function (a, b) {
          return a[str2] > b[str2] ? 1 : a[str2] < b[str2] ? -1 : 0;
        });
        obj.masterdata = obj1;
      }
    }
  }
  prepareTabulatorData() {
    try {
      if (
        $('.table').find('th').length > 0 ||
        $('.list-group-header').length > 0
      ) {
        this.alignTableContents();
        $('head').append(
          '<link rel="stylesheet" href="assets/css/responsive-grid.min.css" type="text/css" />'
        );
      }
    } catch (err) {
      console.log('err ' + err);
    }
  }
  alignTableContents() {
    let activeMode = false;
    if (this.platform.is('mobile') && $(window).width() < 760) {
      activeMode = true;
    }
    insertContents();
    flexTable();

    window.onresize = () => {
      if (
        $('.table').find('th').length > 0 ||
        $('.list-group-header').length > 0
      ) {
        flexTable();
      }
    };
    (window).on('orientationchange resize', function () {
      console.log("orienattion change")
      if (
        $('.table').find('th').length > 0 ||
        $('.list-group-header').length > 0
      ) {
        flexTable();
      }
    });

    function flexTable() {
      if ($(window).width() < 760 || activeMode) {
        // window is less than 768px
        showTableContents();
      } else {
        $('.list-group-header').show();
        $('.datagrid-table').each(function (tabBorderedVal) {
          $('.table').each(function (tabBorderedVal) {
            if ($(this).find('table').length == 0) {
              $(this).find('.table-bordered-thead').show();
              $(this).find('thead').show();
              $(this).find('tr').removeClass('datagrid-table-row');
              $(this).find('tr').find('td').removeClass('datagrid-table-data');
            }
          });
        });
      }
    }

    function showTableContents() {
      $('.list-group-header').hide();
      $('.datagrid-table').each(function (tabBorderedVal) {
        $('.table').each(function (val) {
          if ($(this).find('table').length == 0) {
            $(this).find('.table-bordered-thead').show();
            $(this).find('thead').hide();
            if ($(window).width() < 760) {
              $(this).find('tr').addClass('datagrid-table-row');
              $(this).find('tr').find('td').addClass('datagrid-table-data');
              $('.table').css('table-layout', 'fixed');
            } else {
              $('.table').each(function (tabBorderedVal) {
                if ($(this).find('table').length == 0) {
                  $(this).find('tr').removeClass('datagrid-table-row');
                  $(this)
                    .find('tr')
                    .find('td')
                    .removeClass('datagrid-table-data');
                }
              });

              $('.datagridcard').each(function (datagridcardVal) {
                $('.datagrid-table').each(function (tabBorderedVal) {
                  if ($(this).find('table').length == 0) {
                    $(this).find('tr').addClass('datagrid-table-row');
                    $(this).find('tr').addClass('col-md-4');
                    $(this).find('tr').addClass('col-lg-3');

                    $(this)
                      .find('tr')
                      .find('td')
                      .addClass('datagrid-table-data');
                    $('.datagrid-table-row').css('border', 'none');
                    $('.datagrid-table-row').css('margin-top', '10px');
                  }
                });
              });
            }
          }
        });
      });

      $('.table').each(function (value) {
        if ($(this).find('table').length == 0) {
          var tabledata = $(this).find('td');
          $(tabledata).each(function (val) {
            if (
              $(this)[0].children &&
              $(this)[0].children[1] &&
              $(this)[0].children[1].nodeName != undefined &&
              $(this)[0].children[1].nodeName == 'BUTTON'
            ) {
              if ($(this)[0].innerText.includes('Add')) {
                $(this).find('.table-bordered-thead').hide();
              }
            }
          });
        }
        $(this)
          .find('tfoot')
          .each(function (val) {
            $(this).find('.table-bordered-thead').hide();
          });
      });
    }

    $(document)
      .off()
      .on('click', '.btn', (e) => {
        if ($(window).width() < 760) {
          if (e && e.currentTarget) {
            var val = e.currentTarget.getAttribute('ref');
            var name = e.currentTarget.getAttribute('name');

            if (val != null) {
              if (val.includes('editgrid')) {
                $('.list-group-header').hide();
              } else if (
                val.includes('addRow') ||
                val.includes('removeRow') ||
                (name && name.includes('updateTask')) ||
                (name && name.includes('resetTask'))
              ) {
                $('.list-group-header').hide();
                $('table').find('thead').hide();
                insertContents();
                flexTable();
              }
            } else if (
              e.currentTarget.nodeName === 'BUTTON' &&
              (e.currentTarget.outerHTML.includes('fa-edit') ||
                e.currentTarget.outerHTML.includes('fa-trash'))
            ) {
              if ($('.list-group-header').length > 0) {
                $('.list-group-header').hide();
              }
            }
          }
        }
      });

    function insertContents(this: any) {
      if ($(window).width() < 760) {
        $('.datagrid-table').each(function (tabBorderedVal) {
          $('.table').each(function (obj) {
            if ($(this).find('table').length == 0) {
              let $pathis = $(this);
              // fetch the table heading and map with table row entry
              let tHeadings = $pathis.find('th');
              $pathis.find('tr').each(function (trObj) {
                $(this).addClass('table-row');
                let $thisObj = $(this);
                $thisObj.find('td').each(function (tdObj) {
                  let classList: any;
                  let $this = $(this);
                  if (
                    $(this)[0] &&
                    $(this)[0].firstElementChild &&
                    $(this)[0].firstElementChild.classList
                  ) {
                    classList = $(this)[0].firstElementChild.classList;
                  }
                  if (
                    classList &&
                    (classList.contains('formio-button-remove-row') ||
                      classList.contains('formio-button-add-row'))
                  ) {
                  } else {
                    $this.addClass('table-td');
                    if (
                      $(this).find('.table-bordered-thead').length == 0 &&
                      tHeadings[tdObj] != undefined
                    ) {
                      let text = tHeadings[tdObj].innerText;
                      $this.append(
                        '<div class="table-bordered-thead">' + text + '</div> '
                      );
                      $('.formio-component-label-hidden').css('order', '2');
                      $('.table-bordered-thead').hide();
                    } else if (
                      $(this).find('.table-bordered-thead').length > 0 &&
                      tHeadings[tdObj] != undefined
                    ) {
                      $('.formio-component-label-hidden').css('order', '2');
                    }
                  }
                });
              });
            }
          });
        });

        $('.table').each(function (value) {
          if ($(this).find('table').length == 0) {
            var tabledata = $(this).find('td');
            $(tabledata).each(function (val) {
              if (
                $(this)[0].children &&
                $(this)[0].children[1] &&
                $(this)[0].children[1].nodeName != undefined &&
                $(this)[0].children[1].nodeName == 'BUTTON'
              ) {
                if ($(this)[0].innerText.includes('Add')) {
                  $(this).find('.table-bordered-thead').hide();
                }
              }
            });
          }
        });

        $(this)
          .find('tfoot')
          .each(function (val) {
            $(this).find('.table-bordered-thead').hide();
          });
      }
    }
  }
  async getFormioMasterDataObjects(obj: any, key: any, val: any) {
    var objects = [];

    for (var i in obj) {
      if (typeof obj[i] == 'object') {
        objects = objects.concat(
          this.getFormioMasterDataObjects(obj[i], key, val)
        );
      } else if (i == key && obj[key] == val) {
        var val1 = obj['valueProperty'];
        var val2 = val1.indexOf('.');
        var ress = val1.slice(0, val2);
        var obj1 = [];

        if (this.isHybridNative) {
          let masterdataResourcequery = `SELECT * FROM ${this.constants.MASTER_DATA_TABLE} WHERE RESOURCE_NAME = '${ress}'`;
          let masterdataResults: DbResult =
            await this.unviredSDK.dbExecuteStatement(masterdataResourcequery);
          if (masterdataResults.type == 0) {
            obj1 = masterdataResults.data;
          }
        } else {
          var resource = this.masterData.MASTER_DATA;
          if (resource != undefined && resource != '') {
            for (var x = 0; x < resource.length; x++) {
              var s = resource[x].MASTER_DATA_HEADER.RESOURCE_NAME;
              if (s == ress) {
                obj1.push(JSON.parse(resource[x].MASTER_DATA_HEADER.DATA));
              }
            }
          }
        }

        if (obj.data && typeof obj.data == 'object' && obj.data != undefined) {
          if (!obj.data['json']) {
            obj.masterdata = obj1;
            var tmplt = obj['template'];
            if (tmplt != '') {
              var str1 = tmplt.substring(
                tmplt.lastIndexOf('.') + 1,
                tmplt.length
              );
              var str2 = str1.substring(0, str1.indexOf('}'), str1.length);
              var str3 = '<span>{{' + 'item.' + str2.trim() + '}}</span>';
              obj['template'] = str3;
              obj.idProperty = str2.trim();
            }
            var val = obj['valueProperty'];
            if (val != '') {
              var valProp = val.substring(val.lastIndexOf('.') + 1, val.length);
              obj['valueProperty'] = valProp;
            }
          } else {
            obj.masterdata = obj.data['json'];
          }
        } else if (!obj.data) {
          obj['data'] = {
            values: [],
            json: '',
            url: '',
            resource: '',
            custom: '',
          };
          obj.masterdata = obj1;
          var tmplt = obj['template'];
          if (tmplt != '') {
            var str1 = tmplt.substring(
              tmplt.lastIndexOf('.') + 1,
              tmplt.length
            );
            var str2 = str1.substring(0, str1.indexOf('}'), str1.length);
            var str3 = '<span>{{' + 'item.' + str2.trim() + '}}</span>';
            obj['template'] = str3;
            obj.idProperty = str2.trim();
          }
          var val = obj['valueProperty'];
          if (val != '') {
            var valProp = val.substring(val.lastIndexOf('.') + 1, val.length);
            obj['valueProperty'] = valProp;
          }
        }
      }
    }
    return obj;
  }
  ionViewDidLeave() {
    if (this.cameraOpen) {
      this.cameraOpen = false;
    } else {
      this.shareData.resetFormData();
    }
    document.removeEventListener(
      'displaySDCWorkflowError',
      this.displaySDCWorkflowErrorHandler.bind(this),
      true
    );
  }
  async showForm() {


    if (this.actionButtonClicked == false) {
      // this.selectedStakeholder = null
    }
    this.previousSegmentValue = this.segmentValue
    this.showSaveButton = true
    this.isloaded = true;
    // if (!this.isReport) {
    if (!this.formLoaded) {
      this.formLoaded = true;
      // if (this.devicePlatform != '' && !this.loader.isLoading) {
      //   await this.loader.showBusyIndicator(
      //     this.translate.instant('Loading form...'),
      //     'crescent'
      //   );
      // }
      // await this.getFormTempaltes();
    } else {
      let val = await returnTempData();
      if (val) {
        let tempdata = val.tempData ? val.tempData : val.submission;
        console.log("tempdata", tempdata)
        if (tempdata && tempdata.data) {
          this.formSubmissionData = tempdata.data;
        } else {
          this.formSubmissionData = tempdata;
        }
      }
    }

    // result = false;
    // if(window && window.form && window.form.permitUserApprovalType && window.form.permitUserApprovalType.length > 0){
    console.log("window.form", window.form)
    // let approvalTypeData = window.form.permitUserApprovalType
    // for(let i=0; i< approvalTypeData.length;i++){
    //     if(approvalTypeData[i] && approvalTypeData[i].APPR_TYPE){
    //         if(approvalTypeData[i].APPR_TYPE === 'FRESH_AIR'){

    //             // result = true;
    //         }
    //     }
    // }
    // }

    await this.createFormAndSetFormData();



    // Clear form loading state
    this.isFormLoading = false;
    // } else {

    // }
  }

  async uploadFile(files: any) {
    if (files.length === 0) return;

    for (let i = 0; i < files.length; i++) {
      var mimeType = files[i].type;
      if (mimeType.match(/image\/*/) == null) {
        console.log('Mime type', mimeType)

        let fileAsBlob = new Blob([files[i]], { type: mimeType });
        console.log('File converted to blob:', fileAsBlob);
        let obj: any = {
          thumbnail: '',
          file: fileAsBlob,
          DOC_TYPE: this.getFileExtension(mimeType),
          docItem: {
            FILE_NAME: files[i].name,
            DOC_ID: files[i].name.split('.')[0], // Use filename without extension as DOC_ID fallback
            P_MODE: 'A' // Set P_MODE to 'A' for new attachments to show delete icon
          }
        };
        this.permitDocs.push(obj);
        if (this.loader.isLoading) {
          await this.loader.dismissBusyIndicator();
        }

      } else {
        await this.loader.showBusyIndicator(this.translate.instant('Please wait') + '...', 'crescent');
        let annotatedImageAsBlob: any;
        let compressedImageAsBlob: any;
        let annotatedImage: any;

        var reader = new FileReader();
        reader.readAsDataURL(files[i]);
        reader.onload = async (ev) => {
          annotatedImage = reader.result;
        };

        annotatedImageAsBlob = files[i];
        console.log(
          `original image size ${annotatedImageAsBlob.size / 1024 / 1024} MB`
        );
        const options = {
          maxSizeMB: 1,
          maxWidthOrHeight: 1920,
          useWebWorker: true,
        };
        try {
          compressedImageAsBlob = await imageCompression(
            annotatedImageAsBlob,
            options
          );
          console.log(
            `compressed image size ${compressedImageAsBlob.size / 1024 / 1024} MB`
          );

          if (annotatedImage !== undefined) {
            let obj: any = {
              thumbnail: annotatedImage,
              file: compressedImageAsBlob,
              DOC_TYPE: this.getFileExtension(mimeType),
              docItem: {
                FILE_NAME: files[i].name,
                DOC_ID: files[i].name.split('.')[0], // Use filename without extension as DOC_ID fallback
                P_MODE: 'A' // Set P_MODE to 'A' for new attachments to show delete icon
              }
            };
            this.permitDocs.push(obj);
          }
          if (this.loader.isLoading) {
            await this.loader.dismissBusyIndicator();
          }
        } catch (error) {
          if (this.loader.isLoading) {
            await this.loader.dismissBusyIndicator();
          }
        }
      }
    }
    if (this.loader.isLoading) {
      await this.loader.dismissBusyIndicator();
    }
  }

  findIndex(items: any, item: any) {
    let array = items,
      result = array.findIndex(function (object) {
        return object.thumbnail === item.thumbnail;
      });
    return result;
  }

  //Display Structure image in full screen
  async imageInFullscreen(item: any, fileName: any, index) {
    console.log("image in full screen called", item, fileName)
    let responseData: any;
    let downloadedImage: any;
    // let fileName: any = "";
    let base64data: any;
    let response: any;
    let documentData: any;
    await this.loader.showBusyIndicator(this.translate.instant('Please wait') + '...', 'crescent');
    if (item.file === null) {
      documentData = {
        DOCUMENT: [
          {
            DOCUMENT_HEADER: {
              DOC_ID: item.docItem.DOC_ID,
            },
          },
        ],
      };

      // Fetch and display newly added image if has P_Mode A.
      if (item.docItem.P_MODE == 'A') {
        downloadedImage = this.sanitizer.bypassSecurityTrustResourceUrl(
          item.docItem.THUMBNAIL
        );
        // fileName = item.docItem.DOC_ID;
      } else {
        // For Browser
        if (this.dataService.getDevicePlatform() == 'browser') {
          response = await this.dataService.getDocument(documentData);
          if (response?.code && response.code === 404) {
            this.unviredSDK.logError(
              'InspectionDetailsComponent',
              'imageInFullscreen',
              'Error while Loading Image.'
            );
            if (this.loader.isLoading) {
              await this.loader.dismissBusyIndicator();
            }
          }
          if (response?.DOCUMENT_ATTACHMENT[0] !== undefined) {
            responseData =
              await this.attachmentService.downloadAndWriteAttachmentToFile(
                response.DOCUMENT_ATTACHMENT[0],
                item.docItem.DOC_ID
              );

            var reader = new FileReader();
            reader.readAsDataURL(responseData.body);
            reader.onload = async (_event) => {
              base64data = reader.result;
              // console.log(base64data);
              downloadedImage = this.sanitizer.bypassSecurityTrustResourceUrl(
                `${base64data}`
              );
              // fileName = item.docItem.DOC_ID;
            };
          }
        } else {
          // Fetch, download and display image if network present.
          // if (this.network.type != 'unknown' && this.network.type != 'none') {
          // For Mobile
          let fetchDocumentAttachmentQuery = `select * from document_attachment where fid IN (select LID from document_header where doc_id = '${item.docItem.DOC_ID}')`;
          let fetchDocumentAttachmentQueryResult: any =
            await this.unviredSDK.dbExecuteStatement(
              fetchDocumentAttachmentQuery
            );
          if (
            fetchDocumentAttachmentQueryResult &&
            fetchDocumentAttachmentQueryResult.data.length > 0
          ) {
            // fileName = fetchDocumentAttachmentQueryResult.data[0].FILE_NAME;
            downloadedImage = this.attachmentService.normalizeURL(
              'file://' +
              fetchDocumentAttachmentQueryResult.data[0].LOCAL_PATH
            );
          } else {
            // Download and fetch attachment form doc attachments table and display image.
            response = await this.dataService.getDocument(documentData);
            if (response.DOCUMENT_ATTACHMENT[0] !== undefined) {
              let fetchDocAttachmentQuery = `select * FROM DOCUMENT_ATTACHMENT WHERE UID = '${response.DOCUMENT_ATTACHMENT[0].UID}'`;
              let fetchDocAttachmentQueryResult: any =
                await this.unviredSDK.dbExecuteStatement(
                  fetchDocAttachmentQuery
                );
              if (
                fetchDocAttachmentQueryResult &&
                fetchDocAttachmentQueryResult.data.length > 0
              ) {
                let downloadedAttachment =
                  await this.unviredSDK.downloadAttachment(
                    'DOCUMENT_ATTACHMENT',
                    fetchDocAttachmentQueryResult.data[0]
                  );
                if (downloadedAttachment.type == ResultType.success) {
                  if (downloadedAttachment.data.length > 0) {
                    await this.dataService.sleep(1000);
                    let fetchDocAttachmentQuery1 = `select * FROM DOCUMENT_ATTACHMENT WHERE UID = '${downloadedAttachment.data[0].UID}'`;
                    let fetchDocAttachmentQueryResult1: any =
                      await this.unviredSDK.dbExecuteStatement(
                        fetchDocAttachmentQuery1
                      );
                    if (
                      fetchDocAttachmentQueryResult1 &&
                      fetchDocAttachmentQueryResult1.data.length > 0
                    ) {
                      // fileName = fetchDocAttachmentQueryResult1.data[0].FILE_NAME;
                      downloadedImage = this.attachmentService.normalizeURL(
                        'file://' +
                        fetchDocAttachmentQueryResult1.data[0].LOCAL_PATH
                      );
                    }
                  }
                }
              }
            }
          }
          // }
          //  else {
          //   // Fetch and display image from inspection doc thumbnail if network not present.
          //   downloadedImage = this.sanitizer.bypassSecurityTrustResourceUrl(
          //     item.docItem.THUMBNAIL
          //   );
          // fileName = item.docItem.DOC_ID;
          // }
        }
      }
    } else {
      downloadedImage = item.thumbnail;
    }

    if (this.loader.isLoading) {
      await this.loader.dismissBusyIndicator();
    }
    await this.dataService.sleep(500);
    const modal = await this.modalController.create({
      cssClass: 'full-screen-modal',
      component: ImageFullScreenComponent,
      componentProps: {
        imagePath: downloadedImage,
        imageName: fileName + `${index + 1}`,
      },
    });
    await modal.present();
  }

  async editImage(image: any, index: number) {
    let annotatedImage: any;
    let annotatedImageAsBlob: any;
    let compressedImageAsBlob: any;
    var reader = new FileReader();
    reader.readAsDataURL(image?.file);
    reader.onload = async (ev) => {
      annotatedImage = await this.annotateImage(reader.result);
      await this.loader.showBusyIndicator(this.translate.instant('Please wait') + '...', 'crescent');
      if (
        annotatedImage &&
        annotatedImage != undefined &&
        annotatedImage != null
      ) {
        annotatedImageAsBlob = this.b64toBlob(
          annotatedImage,
          image?.file?.type
        );
        console.log(
          `original image size ${annotatedImageAsBlob.size / 1024 / 1024} MB`
        );
        const options = {
          maxSizeMB: 1,
          maxWidthOrHeight: 1920,
          useWebWorker: true,
        };
        try {
          compressedImageAsBlob = await imageCompression(
            annotatedImageAsBlob,
            options
          );
          compressedImageAsBlob.name = image.file.name;
          console.log(
            `compressed image size ${compressedImageAsBlob.size / 1024 / 1024
            } MB`
          );
          if (annotatedImage !== undefined) {
            let obj: any = {
              thumbnail: annotatedImage,
              file: compressedImageAsBlob,
            };
            let position = this.permitDocs.findIndex(
              (x) => x.file === image.file
            );
            this.permitDocs[position] = obj;
          }
          if (this.loader.isLoading) {
            await this.loader.dismissBusyIndicator();
          }
        } catch (error) {
          if (this.loader.isLoading) {
            await this.loader.dismissBusyIndicator();
          }
          console.log(error);
        }
      } else {
        if (this.loader.isLoading) {
          await this.loader.dismissBusyIndicator();
        }
      }
    };
  }

  captureImage() {
    this.camera.getPicture(this.cameraOptions).then(
      async (imageData) => {
        let base64Image = imageData;
        let annotatedImage: any;
        let annotatedImageAsBlob: any;
        let compressedImageAsBlob: any;

        if (!base64Image.startsWith('data:image/jpeg;base64,')) {
          annotatedImage = await this.annotateImage(
            'data:image/jpeg;base64,' + base64Image
          );
        } else {
          annotatedImage = await this.annotateImage(base64Image);
        }
        await this.loader.showBusyIndicator(this.translate.instant('Please wait') + '...', 'crescent');
        annotatedImageAsBlob = this.b64toBlob(annotatedImage, 'image/jpeg');
        console.log(
          `original image size ${annotatedImageAsBlob.size / 1024 / 1024} MB`
        );
        const options = {
          maxSizeMB: 1,
          maxWidthOrHeight: 1920,
          useWebWorker: true,
        };

        try {
          compressedImageAsBlob = await imageCompression(
            annotatedImageAsBlob,
            options
          );
          console.log(
            `compressed image size ${compressedImageAsBlob.size / 1024 / 1024
            } MB`
          );
          // Generate a meaningful filename for camera captured image
          const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
          const cameraFileName = `camera-image-${timestamp}.jpg`;

          this.permitDocs.push({
            thumbnail: annotatedImage,
            file: compressedImageAsBlob,
            DOC_TYPE: 'IMAGE',
            docItem: {
              FILE_NAME: cameraFileName,
              DOC_ID: `camera-image-${timestamp}`,
              P_MODE: 'A' // Set P_MODE to 'A' for new attachments to show delete icon
            }
          });
          if (this.loader.isLoading) {
            await this.loader.dismissBusyIndicator();
          }
        } catch (error) {
          if (this.loader.isLoading) {
            await this.loader.dismissBusyIndicator();
          }
          console.log(error);
        }
      },
      async (err) => {
        if (this.loader.isLoading) {
          await this.loader.dismissBusyIndicator();
        }
        console.log(err);
      }
    );
  }

  async annotateImage(image) {
    const modal = await this.modalController.create({
      component: AnnotateImageComponent,
      cssClass: 'full-screen-modal',
      backdropDismiss: false,
      id: 'annotateImage',
      componentProps: {
        image: image,
      },
    });
    await modal.present();
    const { data } = await modal.onDidDismiss();
    if (data) {
      return data;
    }
  }

  b64toBlob(b64Data, contentType) {
    contentType = contentType || '';
    var sliceSize = 512;
    let index = b64Data.indexOf(',');
    let strImage = b64Data.substring(index + 1);
    var byteCharacters = atob(strImage);
    var byteArrays = [];

    for (var offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      var slice = byteCharacters.slice(offset, offset + sliceSize);

      var byteNumbers = new Array(slice.length);
      for (var i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      var byteArray = new Uint8Array(byteNumbers);

      byteArrays.push(byteArray);
    }

    var blob = new Blob(byteArrays, { type: contentType });
    return blob;
  }
  //   public toggleStepType(event: IButtonGroupEventArgs): void {
  //     this.stepType = this.stepTypes[event.index].stepType;
  // }


  async generatePDF() {
    console.log("calling generate PDF")
    try {
      if (this.devicePlatform != '' && !this.loader.isLoading) {
        await this.loader.showBusyIndicator(
          this.translate.instant('Generating PDF...'),
          'crescent'
        );
      }
      let headers = {
        Authorization: "Bearer " + localStorage.getItem("PERMIT_token"),
        'Accept': 'application/json',
        'Content-type': 'application/json'
      }
      const httpOptions = {
        // Authorization: "Bearer " + localStorage.getItem("token"),
        headers: headers
      };
      // const body = `queuedExecute=false&messageFormat=custom&permitNo=${this.permit.PERMIT_NO}&submissionId=${this.permitForm.FORM_GUID}`;
      const body = { "permitNo": this.permit.PERMIT_NO, "submissionId": this.permitForm.FORM_GUID }
      this.http.post<any>(
        this.dataService.getUmpUrl() + `/UMP/API/v2/applications/DIGITAL_FORMS/workflow/nustarpermitgeneratepdf`,
        body,
        httpOptions
      ).subscribe({
        next: async (response) => {
          console.log('response', response);
          if (response && response.data) {
            const proxyToDownloadAttachment: string = this.dataService.getUmpUrl() + `/UMP/API/v2/applications/DIGITAL_FORMS/attachments/${response.data}?frontendUser=RAGHAV-UNVIRED-IO-WEB`;
            fetch(proxyToDownloadAttachment, { headers: { Authorization: "Basic dW52aXJlZFxSQUdIQVYtVU5WSVJFRC1JTzp1bnZpcmVk" } })
              .then(async (resp: any) => {
                let errorMessage: string = null;
                switch (resp.status) {
                  case 400:
                    errorMessage = '400 Bad Request';
                    break;
                  case 401:
                    errorMessage = '401 Unauthorized';
                    break;
                  case 404:
                    errorMessage = '400 Not Found';
                    break;
                  case 408:
                    errorMessage = '408 Request Timeout';
                    break;
                }
                if (errorMessage !== null) {
                  if (this.loader.isLoading) {
                    await this.loader.dismissBusyIndicator();
                  }
                  this.dataService.showAlertMessage(
                    'Error', errorMessage
                  );
                  throw new Error(errorMessage);
                } else if (!resp.ok) {
                  if (this.loader.isLoading) {
                    await this.loader.dismissBusyIndicator();
                  }
                  this.dataService.showAlertMessage(
                    'Error', errorMessage
                  );
                  throw new Error('Something went wrong, Please try again!');
                };
                return resp.blob();
              })
              .then(async (blob: any) => {
                if (blob === undefined || blob === null) {
                } else {
                  let downloadLink = document.createElement("a");
                  downloadLink.href = URL.createObjectURL(blob);
                  var today = new Date();
                  var dd = String(today.getDate()).padStart(2, '0');
                  var mm = String(today.getMonth() + 1).padStart(2, '0');
                  var yyyy = today.getFullYear();
                  let filename = `${this.permit.PERMIT_NO + yyyy + '_' + mm + '_' + dd + ".pdf"}`;
                  downloadLink.setAttribute("download", filename);
                  downloadLink.click();
                  if (this.loader.isLoading) {
                    await this.loader.dismissBusyIndicator();
                  }
                }
              })
              .catch(async (error: any) => {
                if (this.loader.isLoading) {
                  await this.loader.dismissBusyIndicator();
                }
                console.log("error")
              });
          } else {
            if (this.loader.isLoading) {
              await this.loader.dismissBusyIndicator();
            }
            this.dataService.showAlertMessage(
              'Alert',
              'No Attchments Id found for permit ' + this.permit.PERMIT_NO
            );
          }
        },
        error: async (err) => {
          if (this.loader.isLoading) {
            await this.loader.dismissBusyIndicator();
          }
          console.log('error', err);
          if (err && err.error && err.error.error) {
            this.dataService.showAlertMessage(
              'Error', err.error.error
            );
          }
        },
        complete: () => console.log('complete'),

      });
    } catch (err: any) {
      if (this.loader.isLoading) {
        await this.loader.dismissBusyIndicator();
      }
      this.dataService.showAlertMessage(
        'Error', err
      );
    }
  }

  async checkPermitFormDuplicate(formId: any) {
    // let query = `SELECT * FROM PERMIT_FORM WHERE PERMIT_NO= '${this.permit.PERMIT_NO}' AND FORM_ID='${formId}' AND P_MODE='A'`;
    let query = `SELECT * FROM PERMIT_FORM WHERE PERMIT_NO= '${this.permit.PERMIT_NO}' AND FORM_ID='${formId}'`;
    let countResult = await this.unviredSDK.dbExecuteStatement(query);
    return countResult;
  }
  async getPermitForm(formId) {
    let formquery = `select * from PERMIT_FORM WHERE PERMIT_NO = '${this.permit.PERMIT_NO}' AND FORM_ID = '${formId}' AND (P_MODE !='D' OR P_MODE IS NULL)`;
    let formqueryResult = await this.unviredSDK.dbExecuteStatement(
      formquery
    );
    if (formqueryResult && formqueryResult.data.length > 0) {
      for (let i = 0; i < formqueryResult.data.length; i++) {
        this.formSubmissionData = JSON.parse(formqueryResult.data[i].DATA);
      }
    }
  }

  async viewSkillCertificate() {
    const modal = await this.modalController.create({
      cssClass: 'full-screen-modal',
      component: SkillsCertificateViewComponent,
    });
    await modal.present();
  }

  syncToServer() {

  }

  isPermitIsExpired(permit: any) {
    if ((permit.IS_EXTENDED == true || permit.IS_EXTENDED == 'true') && this.isExpired(permit.EXTENSION_DATE)) {
      return true
      // permit has expired
    } else if ((permit.IS_EXTENDED == false || permit.IS_EXTENDED == 'false') && this.isExpired(permit.EXPIRY_DATE)) {
      return true
      // permit has expired
    } else {
      return false
      // permit is valid
    }
  }

  isExpired(expiryDate: any): boolean {
    const expiry = new Date(expiryDate);
    const now = new Date();
    return now > expiry;
  }

  async editComments() {
    const modal = await this.modalController.create({
      component: AddPermitCommentsComponent,
      cssClass: this.isMobile ? '' : 'edit-comments-modal',
      id: 'edit-permit-comments',
      componentProps: { comments: JSON.parse(JSON.stringify(this.permit.COMMENTS)) },
    });

    modal.onDidDismiss().then(async (result) => {
      if (result.data) {
        this.permit.COMMENTS = result.data;
        let permitUpdateQuery = `UPDATE PERMIT_HEADER SET P_MODE = 'M', COMMENTS = '${this.permit.COMMENTS}', OBJECT_STATUS = 2 WHERE PERMIT_NO = '${this.permit.PERMIT_NO}'`;
        let permitUpdateQueryResult = await this.unviredSDK.dbExecuteStatement(
          permitUpdateQuery
        );
        if (permitUpdateQueryResult.type == ResultType.success) {
          this.permit.P_MODE = 'M';
          this.permit.OBJECT_STATUS = 2;
        }
        let isAvailableButton = this.permit.hasOwnProperty('isShowDetailsButton');
        if (isAvailableButton) {
          delete this.permit.isShowDetailsButton;
        }
        let isShowReviseButton = this.permit.hasOwnProperty('isShowReviseButton');
        if (isShowReviseButton) {
          delete this.permit.isShowReviseButton;
        }
        //  permitHolder.EXPIRY_DATE = moment(this.extensionForm.controls['endDate'].value).valueOf();
        this.permit.EXTENSION_DATE = moment(this.extensionForm.controls['endDate'].value).valueOf();
        this.permit.IS_EXTENDED = (this.permit.IS_EXTENDED == true || this.permit.IS_EXTENDED == 'true') ? 'true' : 'false';

        let permitformQuery = `SELECT * FROM PERMIT_FORM WHERE PERMIT_NO = '${this.permit.PERMIT_NO}'`;
        let permitformResult = await this.unviredSDK.dbExecuteStatement(
          permitformQuery
        );
        if (permitformResult.type == ResultType.success) {
          console.log("permitformResult" + permitformResult?.data[0].DATA);
          if (permitformResult?.data && permitformResult?.data?.length > 0 && permitformResult.data[0].DATA) {
            let data = JSON.parse(permitformResult.data[0].DATA);
            if (data) { data.permitComments = this.permit.COMMENTS; }
            if (this.subData) {
              this.subData.permitComments = this.permit.COMMENTS;
            }
            let permitUpdateQuery = `UPDATE PERMIT_FORM SET DATA = '${data}' WHERE PERMIT_NO = '${this.permit.PERMIT_NO}' AND FORM_ID = '${permitformResult.data[0].FORM_ID}'`;
            await this.unviredSDK.dbExecuteStatement(permitUpdateQuery);
          }
        }
        console.log("permit in modify permit details ", this.permit)

        // Remove display-only properties before sending to server
        if (this.permit.hasOwnProperty('isShowDetailsButton')) {
          delete this.permit.isShowDetailsButton;
        }
        if (this.permit.hasOwnProperty('isShowReviseButton')) {
          delete this.permit.isShowReviseButton;
        }
        // Remove permitTypeInfo as it's only used for display purposes and should not be sent to server
        if (this.permit.hasOwnProperty('permitTypeInfo')) {
          delete this.permit.permitTypeInfo;
        }

        let permitHeaderResponse: any = await this.dataService.modifyPermit(
          this.permit
        );
        let infoMsg = this.dataService.handleInfoMessage(permitHeaderResponse);
        if (permitHeaderResponse.type == ResultType.success) {
          if (infoMsg && infoMsg?.length > 0) {
            await this.showAlert('Info', infoMsg);
          } else {
            if (this.loader.isLoading) {
              await this.loader.dismissBusyIndicator();
            }
          }
        } else {
          if (
            permitHeaderResponse.message &&
            permitHeaderResponse.message.length > 0
          ) {
            await this.showAlert(
              this.translate.instant('Error'),
              permitHeaderResponse.message
            );
          } else if (
            permitHeaderResponse.error &&
            permitHeaderResponse.error.length > 0
          ) {
            await this.showAlert(
              this.translate.instant('Info'),
              permitHeaderResponse.error
            );
          } else {
            await this.showAlert(
              this.translate.instant('Error'),
              this.translate.instant(
                'Error occured while updating permit, Please try again'
              )
            );
          }
        }
      }
    });
    return await modal.present();
  }

  async editPermitBasicDetails() {
    const modal = await this.modalController.create({
      component: EditPermitDetailsComponent,
      backdropDismiss: true,
      cssClass: this.isMobile ? '' : 'edit-details-modal',
      id: 'edit-permit-basic-details',
      componentProps: { permitHeader: JSON.parse(JSON.stringify(this.permit)) },
    });
    modal.onDidDismiss().then(async (result) => {
      if (result.data) {
        this.permit.PERMIT_TYPE = result.data.PERMIT_TYPE;
        this.permit.FACILITY_ID = result.data.FACILITY_ID;
        this.permit.DIVISION_ID = result.data.DIVISION_ID;
        this.permit.DESCRIPTION = result.data.DESCRIPTION;
        this.permit.TAG = result.data.TAG;
        this.permit.JOB_NO = result.data.JOB_NO;
      }
    });
    return await modal.present();
  }

  async editPermitAgents() {
    const modal = await this.modalController.create({
      component: EditPermitAgentsComponent,
      backdropDismiss: true,
      cssClass: this.isMobile ? '' : 'edit-agents-modal',
      id: 'edit-permit-agenets',
      componentProps: { permit: JSON.parse(JSON.stringify(this.permit)) },
    });
    modal.onDidDismiss().then(async (result) => {
      if (result.data) {
        this.permit.AGENT_ID_EXT = result.data.AGENT_ID_EXT;
        this.permit.AGENT_ID_INT = result.data.AGENT_ID_INT;
      }
    });
    return await modal.present();
  }

  async editPermitValidity() {
    const modal = await this.modalController.create({
      component: EditPermitValidityComponent,
      backdropDismiss: true,
      cssClass: this.isMobile ? '' : 'edit-validity-modal',
      id: 'edit-permit-validity',
      componentProps: { permit: JSON.parse(JSON.stringify(this.permit)) },
    });
    modal.onDidDismiss().then(async (result) => {
      if (result.data) {
        this.permit.PERMIT_DATE = new Date(result.data.PERMIT_DATE).toISOString();
        this.permit.EXPIRY_DATE = new Date(result.data.EXPIRY_DATE).toISOString();
      }
    });
    return await modal.present();
  }

  getHolderApproveCount() {
    const count = this.stakeHoldersList.filter(item => (item.ROLE == 'APPROVE' && item.P_MODE == null)).length;
    return count == 0 ? true : false;
  }

  getFileExtension(mimeType) {
    const mimeToExtMap = {
      'image/jpeg': 'IMAGE',
      'image/png': 'IMAGE',
      'application/pdf': 'PDF',
      'text/plain': 'TEXT',
      'application/msword': 'WORD',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'DOCUMENT',
      'application/vnd.ms-excel': 'EXCEL',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'EXCEL',
      'application/vnd.ms-powerpoint': 'PPT',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PPT',
      'text/csv': 'CSV'
    };
    return mimeToExtMap[mimeType] || 'DOCUMENT';
  }

  getDownloadFileExtension(mimeType) {
    const mimeToExtMap = {
      'PDF': 'pdf',
      'EXCEL': 'xlsx',
      'TEXT': 'txt',
      'WORD': 'docx',
      'DOCUMENT': 'docx',
      'PPT': 'pptx',
      'CSV': 'csv'
    };
    return mimeToExtMap[mimeType] || '.pdf';
  }

  async downloadFile(item: any) {
    let responseData: any;
    let response: any;
    let documentData: any;
    await this.loader.showBusyIndicator(this.translate.instant('Please wait') + '...', 'crescent');
    if (item.file === null) {
      documentData = {
        DOCUMENT: [
          {
            DOCUMENT_HEADER: {
              DOC_ID: item.docItem.DOC_ID,
            },
          },
        ],
      };

      if (this.dataService.getDevicePlatform() == 'browser') {
        response = await this.dataService.getDocument(documentData);
        if (response?.code && response.code === 404) {
          this.unviredSDK.logError(
            'InspectionDetailsComponent',
            'imageInFullscreen',
            'Error while Loading Image.'
          );
          if (this.loader.isLoading) {
            await this.loader.dismissBusyIndicator();
          }
        }
        if (response?.DOCUMENT_ATTACHMENT[0] !== undefined) {
          responseData =
            await this.attachmentService.downloadAndWriteAttachmentToFile(
              response.DOCUMENT_ATTACHMENT[0],
              item.docItem.DOC_ID
            );

          const blob = new Blob([responseData.body], { type: this.getDownloadFileExtension(item.DOC_TYPE) });
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = item.docItem.DOC_ID + '.' + this.getDownloadFileExtension(item.DOC_TYPE);
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
        }
      }
    } else {
      const blob = new Blob([item.file], { type: this.getDownloadFileExtension(item.DOC_TYPE) });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      // Use original filename if available, otherwise use DOC_ID with proper extension
      const fileName = item.docItem?.FILE_NAME || (item.docItem?.DOC_ID + '.' + this.getDownloadFileExtension(item.DOC_TYPE));
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    }

    if (this.loader.isLoading) {
      await this.loader.dismissBusyIndicator();
    }
  }


  async getInternalAndExternalAgents() {
    let userAgent = await this.dataService.getData('AGENT_HEADER');
    let getUserAgentData: any[] = [];
    let result: any;


    this.filteredInternalList = JSON.parse(JSON.stringify(this.stakeHoldersList));
    this.filteredExternalList = JSON.parse(JSON.stringify(this.stakeHoldersList));

    let internalU: any = userAgent.filter(ele => ele.IS_INTERNAL == 'true')

    let externalU = userAgent.filter(ele => ele.IS_INTERNAL == 'false')


    console.log("the stakeholder in getinternal", this.stakeHoldersList)

    for await (let element of userAgent) {
      let customData = {
        AGENT_USER: [
          {
            AGENT_USER_HEADER: {
              AGENT_ID: element?.AGENT_ID,
            },
          },
        ],
      };

      result = await this.dataService.getAgentUser(customData);
      getUserAgentData.push(result);
    }


    let userapprovals = await this.dataService.getData('USER_APPROVAL_TYPE', ``, 'APPR_TYPE ASC');

    console.log("this.userapprovals after get", userapprovals)

    for (let i = 0; i < getUserAgentData.length; i++) {
      let intUsers = internalU.flatMap(ele => getUserAgentData[i].data.AGENT_USER.filter(el => el.AGENT_USER_HEADER.AGENT_ID == ele.AGENT_ID))

      console.log("intUsers", intUsers)
      console.log("getUserAgentData[i].data", getUserAgentData[i].data)
      let intUAppr = intUsers.flatMap(ele => userapprovals.filter(el => el.USER_ID == ele.AGENT_USER_HEADER.USER_ID))
      this.internalApprType.push(intUAppr)
      this.internalApprType = this.internalApprType.flatMap(ele => ele);
    }

    console.log("internalApprtype", this.internalApprType)

    for (let i = 0; i < getUserAgentData.length; i++) {
      let extUsers = externalU.flatMap(ele => getUserAgentData[i].data.AGENT_USER.filter(el => el.AGENT_USER_HEADER.AGENT_ID == ele.AGENT_ID))
      let extUAppr = extUsers.flatMap(ele => userapprovals.filter(el => el.USER_ID == ele.AGENT_USER_HEADER.USER_ID))
      this.externalApprType.push(extUAppr)
      this.externalApprType = this.externalApprType.flatMap(ele => ele);
    }

    console.log("externalApprType", this.externalApprType)
    this.filteredInternalList.forEach((stake, i) => {
      let context = stake.CONTEXT; //FINAL
      let conUsers = this.internalApprType.filter(ele => ele.USER_APPROVAL_TYPE_HEADER.APPR_TYPE == context);
      let users: any = [];
      if (context == null && stake.ROLE == 'REQUEST') {

        stake.users.forEach(ele => {
          if (ele.USER_ID == stake.USER_ID && Object.keys(ele).includes(stake.ROLE)) {
            users.push(ele)
          }
        });

      } else {
        if (context == null && stake.ROLE == 'REVIEW') {
          stake.users.forEach(user => {

            const matchedUser = this.dataService.internalUsersArray.find(internalUser => internalUser.USER_ID === user.USER_ID);
            if (matchedUser) {
              users.push(user)
            } else {
              // console.log("Unmatched USER_ID:", user.USER_ID);
            }
          });

        } else {
          conUsers.forEach(user => {
            stake.users.forEach(ele => {
              if (ele.USER_ID == user.USER_APPROVAL_TYPE_HEADER.USER_ID) {
                users.push(ele)
              }
            })
          })

        }

      }
      stake.users = users;
      // this.filteredUsers = users
      // console.log("this.filteredUsers" , this.filteredUsers)
    })


    // //for external users
    this.filteredExternalList.forEach((stake, i) => {
      let context = stake.CONTEXT; //FINAL
      let conUsers = this.externalApprType.filter(ele => ele.USER_APPROVAL_TYPE_HEADER.APPR_TYPE == context);
      let users: any = [];
      if (context == null && stake.ROLE == 'REQUEST') {

        stake.users.forEach(ele => {
          if (ele.USER_ID == stake.USER_ID && Object.keys(ele).includes(stake.ROLE)) {
            users.push(ele)
          }
        });
      } else {

        if (context == null && stake.ROLE == 'REVIEW') {
          stake.users.forEach(user => {
            const matchedUser = this.dataService.externalUsersArray.find(externalUser => externalUser.USER_ID === user.USER_ID);
            if (matchedUser) {
              users.push(user)
            } else {
              // console.log("Unmatched USER_ID:", user.USER_ID);
            }
          });

        } else {
          conUsers.forEach(user => {
            stake.users.forEach(ele => {
              if (ele.USER_ID == user.USER_APPROVAL_TYPE_HEADER.USER_ID) {
                users.push(ele)
              }
            })
          })
        }

      }
      stake.users = users;
      // console.log(stake)
    })


    const mergedResult = this.mergeBothUsers(this.filteredInternalList, this.filteredExternalList);
    this.stakeHoldersList = mergedResult;
    console.log("this.stakeHoldersList", mergedResult)
  }


  async filterUsersOnApprovalType() {
    let filteredArray = [];
    let internalUsers = [];
    let externalUsers = [];
    // console.log("filteredArray called" , this.stakeHoldersList)

    let userapprovals = await this.unviredSDK.dbSelect('USER_APPROVAL_TYPE', ``)
    // console.log("userapprovals" ,userapprovals.data)


    // let getPermitDocsquery = `SELECT * FROM USER_APPROVAL_TYPE WHERE ROLE='APPROVE' AND USER_ID= '${this.permit.PERMIT_NO}'`;
    // let getPermitDocsqueryResult = await this.unviredSDK.dbExecuteStatement(
    //   getPermitDocsquery
    // );
    // if (getPermitDocsqueryResult?.data?.length > 0) {
    //   console.log("getPermitDocsqueryResult" , getPermitDocsqueryResult)
    // }

    // console.log("filteredUsers" , this.filteredUsers)
    this.filteredUsers.forEach(async (currentUsers, index) => {
      // Ensure the current element is an array
      if (!Array.isArray(currentUsers)) {
        console.warn(`Element at index ${index} is not an array. Replacing with an empty array.`);
        this.filteredUsers[index] = [];
        return;
      }

      // Filter internal and external users
      internalUsers = currentUsers.filter(user =>
        this.dataService.internalUsersArray.some(data => data.USER_ID === user.USER_ID)
      );

      externalUsers = currentUsers.filter(user =>
        this.dataService.externalUsersArray.some(data => data.USER_ID === user.USER_ID)
      );

      // console.log(`Internal Users at index ${index}:`, internalUsers);
      // console.log(`External Users at index ${index}:`, externalUsers);

      // let userapprovals =  this.dataService.getData('USER_APPROVAL_TYPE', ``, 'APPR_TYPE ASC').then((res) =>{
      //   console.log("this.userapprovals after get" , res )
      // })


      // console.log("internalUsers" , internalUsers);
      // console.log("externalUsers" , externalUsers)




      // console.log("this.internalApprtype" , this.internalApprType)
      const result = this.stakeHoldersList.flatMap(stakeholder => {
        return stakeholder.users.flatMap(user => {
          // Find matching approvals from internalApprType
          const matchingApprovals = this.internalApprType.filter(
            approval =>
              approval.USER_APPROVAL_TYPE_HEADER.USER_ID === user.USER_ID &&
              approval.USER_APPROVAL_TYPE_HEADER.APPR_TYPE === stakeholder.APPR_TYPE
          );

          // Generate the result objects for matches
          return matchingApprovals.map(approval => ({
            ...user,
            APPR_TYPE: approval.USER_APPROVAL_TYPE_HEADER.APPR_TYPE,
            PERMIT_NO: stakeholder.PERMIT_NO,
            ROLE: stakeholder.ROLE,
          }));
        });
      });

      // Output the result
      // console.log("result is ",result);

      // console.log(this.internalApprType)

      // let fInternalApp =  internalUsers.filter(user =>

      //   this.internalApprType.some(ele=>ele.USER_APPROVAL_TYPE_HEADER.USER_ID == user.USER_ID && user.REVIEW == "true")
      // );

      // let fExternalApp =  externalUsers.filter(user =>
      //   this.externalApprType.some(ele=>ele.USER_APPROVAL_TYPE_HEADER.USER_ID == user.USER_ID && user.REVIEW == "true")
      // );



      //     let fInternalApp = internalUsers.filter(user => user.REVIEW === "true" &&
      //       this.internalApprType.some( ele => ele.USER_APPROVAL_TYPE_HEADER.USER_ID === user.USER_ID))
      //     .map(user => {
      //   const matchingApproval = this.internalApprType.find(
      //     ele => ele.USER_APPROVAL_TYPE_HEADER.USER_ID === user.USER_ID
      //   );
      //   return matchingApproval
      //     ? { ...user, APP_TYPE: matchingApproval.USER_APPROVAL_TYPE_HEADER.APPR_TYPE }
      //     : user;
      // });




      let fInternalApp = internalUsers
        .filter(user => user.REVIEW === "true")
        .flatMap(user => {
          // Find all matching approvals for the user
          const matchingApprovals = this.internalApprType.filter(
            ele => ele.USER_APPROVAL_TYPE_HEADER.USER_ID === user.USER_ID
          );

          // Return a new object for each APPR_TYPE
          return matchingApprovals.map(approval => ({
            ...user,
            APP_TYPE: approval.USER_APPROVAL_TYPE_HEADER.APPR_TYPE
          }));
        });


      // console.log("fInternalApp" , fInternalApp);
    });
    console.log("stakeholderslist", this.stakeHoldersList)

  }



  mergeBothUsers(filteredInternalList: any, filteredExternalList: any) {
    const mergedList: any[] = [...filteredInternalList];

    filteredExternalList.forEach(externalItem => {
      const correspondingInternalItem = mergedList.find(
        internalItem => internalItem.ROW_ID === externalItem.ROW_ID
      );

      if (correspondingInternalItem) {
        externalItem.users.forEach(externalUser => {
          externalUser.LAST_NAME += '  -  ( External )';
          if (!correspondingInternalItem.users.some(user => user.USER_ID === externalUser.USER_ID)) {
            correspondingInternalItem.users.push(externalUser);
          }
        });
      } else {

        const modifiedExternalItem = { ...externalItem, users: externalItem.users.map(user => ({ ...user, LAST_NAME: user.LAST_NAME + '-EXTERNAL' })) };
        mergedList.push(modifiedExternalItem);
      }
    });

    return mergedList;

  }


  onStartDateChange(event: any, startDateValue: any): void {
    console.log("startDateValue", startDateValue, event)
    console.log("value is", this.extensionForm.controls['startDate'].value);
    if (startDateValue) {
      const startDate = new Date(startDateValue);
      console.log("startDate isssssssss", startDate, this.permit.EXPIRY_DATE)
      this.updateEndDateRange(startDate);

      this.extensionForm.controls['endDate'].setValue(this.getDefaultEndDate(startDate));
    } else {
      this.minEndDate = null;
      this.maxEndDate = null;
    }
  }


  updateEndDateRange(startDate: Date): void {
    if (isNaN(startDate.getTime())) {
      console.error("Invalid startDate provided:", startDate);
      this.minEndDate = null;
      this.maxEndDate = null;
      return;
    }
    const minDate = startDate;
    const maxDate = new Date(startDate.getTime() + 2 * 60 * 60 * 1000);
    this.minEndDate = this.formatDateToLocalISOString(minDate);
    this.maxEndDate = this.formatDateToLocalISOString(maxDate);

  }

  formatDateToLocalISOString(date: Date): string {
    const offset = date.getTimezoneOffset() * 60000;
    return new Date(date.getTime() - offset).toISOString().slice(0, -1);
  }

  getDefaultEndDate(startDate: Date): string {
    const endDate = new Date(startDate);
    endDate.setHours(startDate.getHours() + 2);
    return this.formatDateToLocalISOString(endDate);
  }

  onEndDateChange(event: any): void {
    console.log("event", event.target.value);
  }


  hasExecuteRole(): boolean {
    let execCount: boolean = false;
    const hasExecuteRole = this.stakeHoldersList.filter(s => s.ROLE === 'EXECUTE').length;
    const hasExecuteRoleButNotProcessed = this.stakeHoldersList.filter(s => s.ROLE === 'EXECUTE' && s.PROCESSED_ON == null).length;
    const execArray = this.stakeActionStatusArray.filter(s => s.status === 'Executed').length;

    if (execArray === hasExecuteRole || hasExecuteRoleButNotProcessed == execArray) {
      execCount = false;
    } else {
      execCount = true;
    }

    return execCount;
  }


  async goBackToLastLocation(formsubmissiondata: any) {
    let isFormDataChanged: boolean = false;
    let lastChanges
    try {
      let lastFormData = await returnLastTempData();
      if (lastFormData.error == 'validation error') {
        console.log("the selected stakeholder in lastchanges is",)

        lastChanges = JSON.stringify(lastFormData.tempData.data)
        console.log("lastChanges", lastChanges);


        console.log("lastChanges", lastChanges);
        // console.log("lastChanges" , lastChanges);
        // let permitformUpdateQuery = `UPDATE PERMIT_FORM SET PARTIAL_FLAG = ${AppConstants.YES} WHERE PERMIT_NO ='${this.permit.PERMIT_NO}'`;

        // let permitformUpdateResult = await this.unviredSDK.dbExecuteStatement(
        //   permitformUpdateQuery
        // );
        // if (permitformUpdateResult.type == ResultType.success) {
        //   // this.unviredSDK.dbExportWebData()
        //   console.log("permitformUpdateResult" + permitformUpdateResult);
        // } else {
        //   console.log("FAILURE - update permit form details in DB")
        // }

      } else {

        this.selectedStakeholder.CONTEXT = 'FORM_COMPLETED'
        const stakeholderIndex = this.stakeHoldersList.findIndex(
          stakeholder => stakeholder.ROW_ID === this.selectedStakeholder.ROW_ID,

        );

        if (stakeholderIndex !== -1) {
          // this.stakeHoldersList[stakeholderIndex].CONTEXT = 'FORM_COMPLETED';
          this.actionButtons(this.stakeHolderActionStatus, this.stakeHoldersList[stakeholderIndex].ROW_ID, this.stakeHoldersList[stakeholderIndex])
          console.log("Updated stakeholder:", this.stakeHoldersList[stakeholderIndex]);
          //  alert('FORM_COMPLETED')
        }


        let stakeHolderUpdateQuery = `UPDATE PERMIT_STAKEHOLDER SET P_MODE = 'M' , CONTEXT = 'FORM_COMPLETED', OBJECT_STATUS = 2 WHERE PERMIT_NO = '${this.permit.PERMIT_NO}' AND ROW_ID = '${this.selectedStakeholder.ROW_ID}'`;
        let stakeHolderUpdateQueryResult = await this.unviredSDK.dbExecuteStatement(
          stakeHolderUpdateQuery
        );
        if (
          stakeHolderUpdateQueryResult &&
          stakeHolderUpdateQueryResult.data
        ) {
          console.log("stakeHolderUpdateQueryResult", stakeHolderUpdateQueryResult.data)
          // this.unviredSDK.dbExportWebData()
        }
        lastChanges = JSON.stringify(lastFormData.submission.data);
        console.log("lastChanges", lastChanges);

        let hashValueWithLastChanges = hash(lastChanges);

        let data = localStorage.getItem("initialFormData");
        let hashValueWithInitialData = hash(data);
        let diff = hashValueWithLastChanges.localeCompare(hashValueWithInitialData);
        isFormDataChanged = (diff === 0) ? false : true;
      }



    } catch (error) {
      this.unviredSDK.logError('FormRendererPage', 'returnLastTempData()', 'ERROR: ' + error);
    }
    if (isFormDataChanged && (!this.formReadOnlyFlag)) {
      // CHANGE IN submission DATA
      console.log(" data changed")
      await this.updateDB(
        formsubmissiondata
      );
    } else {
      // NO CHANGE IN DATA
      console.log(" no data changed in form submission")
    }
  }


  async actionButtons(status: string, i: number, stakeHolder: any) {


    if (stakeHolder.USER_ID != null) {

      if (stakeHolder.USER_ID == this.userObject.USER_ID) {


        console.log("stakeHolder.ROW_ID", stakeHolder.ROW_ID)
        if (stakeHolder.ROLE == 'REVIEW' && stakeHolder.APPR_TYPE == null) {
          console.log("the data is ", this.hasReviewersWithNullUserId(), !this.canShowRejectButton(stakeHolder))
          if (this.hasReviewersWithNullUserId() && !this.canShowRejectButton(stakeHolder)) {

            this.dataService.showAlertMessage(
              'Alert',
              'Please review all approval types '
            );
          }

          let hasExecuteRole = this.stakeHoldersList.some(item => item.ROLE === 'EXECUTE' && item.PROCESSED_ON == null);
          if (this.dataService.isRevised == true && !hasExecuteRole || !hasExecuteRole) {
            this.dataService.showAlertMessage(
              'Alert',
              'Please add atleast one execute role before approving the review'
            );
          }
          else {
           
          
            console.log("the status is " , status)
             if(status =='Approved'){
              
              this.stakeActionStatusArray.push({ROW_ID: stakeHolder.ROW_ID , status: 'Approved' , comment: ''  , stakeData: stakeHolder})
          
              this.shouldShowApproveButtonInStake()
              // this.mainReviewerApproved = true

              // this.hasApprovedProcessedStakeholder = true;
              // let permitLogObj =  new PERMIT_LOG();
              // permitLogObj.PERMIT_NO = stakeHolder.PERMIT_NO;
              // permitLogObj.OBJECT_STATUS = 1;
              // permitLogObj.P_MODE = 'A';
              // permitLogObj.FID = this.permit.LID;
              // permitLogObj.APPR_TYPE = stakeHolder.APPR_TYPE
              // permitLogObj.LOG_NO = await this.findMaxLogNumberFromDb(
              //   this.permit.PERMIT_NO
              // );
              // permitLogObj.CREATED_ON = new Date().getTime();
              // permitLogObj.CREATED_BY = `${this.userObject.USER_ID}`;




              //  permitLogObj.PERMIT_STATUS = 'IN_REVIEW'
              //  permitLogObj.APPROVAL = 'A'
              this.permit.APPROVAL_SUMMARY = 'A'
              //  permitLogObj.COMMENT = `${permitLogObj.CREATED_BY
              //  } create permit on ${moment().format('MMMM Do YYYY, h:mm:ss a')}`;


              //  console.log("the permit obj after action is " , this.permit , permitLogObj)
              //  let result = await this.unviredSDK.dbInsert('PERMIT_LOG', permitLogObj , false )

              //  if (result.type == ResultType.success) {


              //  } else{
              //    console.error("Error while approving the action")
              //  }

            } else if (status == 'Rejected') {

              if (status == 'Rejected') {
                // let comments = await this.addCommentsToReject(stakeHolder , true , i);
                // console.log("comments is " , comments)
                // this.stakeActionStatusArray.push({ROW_ID: stakeHolder.ROW_ID , status: 'Rejected' , comment: ''})
                // permitLogObj.COMMENT = comments

                this.stakeActionStatusArray.push({ROW_ID: stakeHolder.ROW_ID , status: 'Rejected', comment: ''  , stakeData: stakeHolder})
              }



              // this.permit.isRejected = true
              //      permitLogObj.PERMIT_STATUS = 'IN_REVIEW'
              //      permitLogObj.APPROVAL = 'R'
              //       this.permit.APPROVAL_SUMMARY = 'R';

              this.stakeHoldersList.some(item =>
                item.actionButtonStatus === "Rejected"

              );




              let rejectedIndex = this.stakeHoldersList.findIndex(item => item.actionButtonStatus === "Rejected");


              this.stakeHoldersList.forEach((item, index) => {
                item.hasRejectedStatusChange = rejectedIndex !== -1 && index > rejectedIndex;

              });


            } else if (status == 'Executed') {
              console.log("execute status entered")
              this.stakeActionStatusArray.push({ROW_ID: stakeHolder.ROW_ID , status: 'Executed' , comment: ''  , stakeData: stakeHolder})
              this.hasExecuteRole()
              this.hidePerformButton(stakeHolder.ROW_ID)
              // this.stakeActionStatusArray.push({ROW_ID: stakeHolder.ROW_ID , status: 'Approved' , comment: ''})
              let permitLogObj = new PERMIT_LOG();
              permitLogObj.PERMIT_NO = stakeHolder.PERMIT_NO;
              permitLogObj.OBJECT_STATUS = 1;
              permitLogObj.P_MODE = 'A';
              permitLogObj.FID = this.permit.LID;
              permitLogObj.APPR_TYPE = ''
              permitLogObj.LOG_NO = await this.findMaxLogNumberFromDb(
                this.permit.PERMIT_NO
              );
              permitLogObj.CREATED_ON = new Date().getTime();
              permitLogObj.CREATED_BY = `${this.userObject.USER_ID}`;




              permitLogObj.PERMIT_STATUS = 'ISSUED'
              permitLogObj.APPROVAL = 'A'
              permitLogObj.ACTION = 'TRANSITION'
              //  this.permit.APPROVAL_SUMMARY = 'A'
              permitLogObj.COMMENT = `${permitLogObj.CREATED_BY
                } create permit on ${moment().format('MMMM Do YYYY, h:mm:ss a')}`;


              console.log("the permit obj after action is ", this.permit, permitLogObj)
              let result = await this.unviredSDK.dbInsert('PERMIT_LOG', permitLogObj, false)

               if (result.type == ResultType.success) {
             
               
               } else{
                 console.error("Error while approving the action")
               }
            }  else if(status == 'Confirmed'){
              this.stakeActionStatusArray.push({ROW_ID: stakeHolder.ROW_ID , status: 'Confirmed' , comment: '' , stakeData: stakeHolder})
              this.hideConfirmButton(stakeHolder.ROW_ID)
              this.cdr.detectChanges();
              // this.stakeActionStatusArray.push({ROW_ID: stakeHolder.ROW_ID , status: 'Approved' , comment: ''})
              let permitLogObj = new PERMIT_LOG();
              permitLogObj.PERMIT_NO = stakeHolder.PERMIT_NO;
              permitLogObj.OBJECT_STATUS = 1;
              permitLogObj.P_MODE = 'A';
              permitLogObj.FID = this.permit.LID;
              permitLogObj.APPR_TYPE = ''
              permitLogObj.LOG_NO = await this.findMaxLogNumberFromDb(
                this.permit.PERMIT_NO
              );
              permitLogObj.CREATED_ON = new Date().getTime();
              permitLogObj.CREATED_BY = `${this.userObject.USER_ID}`;




              permitLogObj.PERMIT_STATUS = 'ISSUED'
              permitLogObj.ACTION = 'TRANSITION'
              permitLogObj.APPROVAL = 'A'
              //  this.permit.APPROVAL_SUMMARY = 'A'
              permitLogObj.COMMENT = `${permitLogObj.CREATED_BY
                } create permit on ${moment().format('MMMM Do YYYY, h:mm:ss a')}`;


              console.log("the permit obj after action is ", this.permit, permitLogObj)
              let result = await this.unviredSDK.dbInsert('PERMIT_LOG', permitLogObj, false)

               if (result.type == ResultType.success) {
             
               
               } else{
                 console.error("Error while approving the action")
               }
              }else if(status == 'Closed'){

                this.stakeActionStatusArray.push({ROW_ID: stakeHolder.ROW_ID , status: 'Closed' , comment: '' , stakeData: stakeHolder})
              this.hideCloseButton(stakeHolder.ROW_ID)
              this.cdr.detectChanges();
              // this.stakeActionStatusArray.push({ROW_ID: stakeHolder.ROW_ID , status: 'Approved' , comment: ''})
              let permitLogObj =  new PERMIT_LOG();
              permitLogObj.PERMIT_NO = stakeHolder.PERMIT_NO;
              permitLogObj.OBJECT_STATUS = 1;
              permitLogObj.P_MODE = 'A';
              permitLogObj.FID = this.permit.LID;
              permitLogObj.APPR_TYPE = ''
              permitLogObj.LOG_NO = await this.findMaxLogNumberFromDb(
                this.permit.PERMIT_NO
              );
              permitLogObj.CREATED_ON = new Date().getTime();
              permitLogObj.CREATED_BY = `${this.userObject.USER_ID}`;
            
              
        
      
               permitLogObj.PERMIT_STATUS = 'CLOSED'
               permitLogObj.ACTION = 'TRANSITION' 
               permitLogObj.APPROVAL = 'A'
              //  this.permit.APPROVAL_SUMMARY = 'A'
               permitLogObj.COMMENT = `${permitLogObj.CREATED_BY
               } closed permit on ${moment().format('MMMM Do YYYY, h:mm:ss a')}`;


               console.log("the permit obj after action is " , this.permit , permitLogObj)
               let result = await this.unviredSDK.dbInsert('PERMIT_LOG', permitLogObj , false )

               if (result.type == ResultType.success) {
             
               
               } else{
                 console.error("Error while approving the action")
               }

              }
        
         
          }


        } else {
          console.log("created new permit log")
            // let permitLogObj =  new PERMIT_LOG();
            // permitLogObj.PERMIT_NO = stakeHolder.PERMIT_NO;
            // permitLogObj.OBJECT_STATUS = 1;
            // permitLogObj.P_MODE = 'A';
            // permitLogObj.FID = this.permit.LID;
            // permitLogObj.APPR_TYPE = stakeHolder.APPR_TYPE
            // permitLogObj.LOG_NO = await this.findMaxLogNumberFromDb(
            //   this.permit.PERMIT_NO
            // );
            // permitLogObj.CREATED_ON = new Date().getTime();
            // permitLogObj.CREATED_BY = `${this.userObject.USER_ID}`;
          
        
             if(status == 'Approved'){
              
        
           
              const existingIndex = this.stakeActionStatusArray.findIndex(item => 
                item.ROW_ID === stakeHolder.ROW_ID
              );
              
              if (existingIndex === -1) {
                // Object doesn't exist, push new object
                this.stakeActionStatusArray.push({
                  ROW_ID: stakeHolder.ROW_ID, 
                  status: 'Approved',
                  comment: '',
                  stakeData: stakeHolder
                });
              } else {
                // Object exists, update its status
                this.stakeActionStatusArray[existingIndex].status = 'Approved';
                this.stakeActionStatusArray[existingIndex].comment = '';
              }

              //  permitLogObj.PERMIT_STATUS = 'IN_REVIEW'
              //  permitLogObj.APPROVAL = 'A'
              //  this.permit.APPROVAL_SUMMARY = 'A'
              //  permitLogObj.COMMENT = `${permitLogObj.CREATED_BY
              //  } create permit on ${moment().format('MMMM Do YYYY, h:mm:ss a')}`;
             
            } else if(status == 'Rejected'){
              const existingIndex = this.stakeActionStatusArray.findIndex(item => 
                item.ROW_ID === stakeHolder.ROW_ID
              );
              
              if (existingIndex === -1) {
                // Object doesn't exist, push new object
                if(status == 'Rejected'){
                
              
                  // let comments = await this.addCommentsToReject(stakeHolder , true , i);
                  // console.log("comments is " , comments)
                  this.stakeActionStatusArray.push({ROW_ID: stakeHolder.ROW_ID , status: 'Rejected', comment: ''  , stakeData: stakeHolder})
                }
              } else {
                // Object exists, update its status
                this.stakeActionStatusArray[existingIndex].status = 'Rejected';
                this.stakeActionStatusArray[existingIndex].comment = '';
              }
             
              stakeHolder.actionButtonStatus = 'Rejected';
              // this.permit.isRejected = true
     
             this.stakeHoldersList.some(item => 
                  item.actionButtonStatus === "Rejected"
  
               );
  
    
          
           
             let rejectedIndex = this.stakeHoldersList.findIndex(item => item.actionButtonStatus === "Rejected");
           
           
           this.stakeHoldersList.forEach((item, index) => {
             item.hasRejectedStatusChange = rejectedIndex !== -1 && index > rejectedIndex;
             
           });
           
              
            } else if(status == 'Executed') {

              const existingIndex = this.stakeActionStatusArray.findIndex(item => 
                item.ROW_ID === stakeHolder.ROW_ID
              );
              
              if (existingIndex === -1) {
                if(status == 'Executed'){
                  this.stakeActionStatusArray.push({ROW_ID: stakeHolder.ROW_ID , status: 'Executed' , comment: ''  , stakeData: stakeHolder})
                  this.hasExecuteRole()
                }
              }else {
                // Object exists, update its status
                this.stakeActionStatusArray[existingIndex].status = 'Executed';
                this.stakeActionStatusArray[existingIndex].comment = '';
              }





            // let inspectionLogUpdateQuery = `UPDATE PERMIT_STAKEHOLDER SET OBJECT_STATUS = 2, P_MODE = 'M'  WHERE PERMIT_NO = '${stakeHolder.PERMIT_NO}' AND ROW_ID = '${stakeHolder.ROW_ID}'`;
            // let stakeHolderUpdateQueryResult = await this.unviredSDK.dbExecuteStatement(
            //   inspectionLogUpdateQuery
            // );
            // if (
            //   stakeHolderUpdateQueryResult &&
            //   stakeHolderUpdateQueryResult.data
            // ) {
            //   console.log("stakeHolderUpdateQueryResult" , stakeHolderUpdateQueryResult.data)
            //   // this.unviredSDK.dbExportWebData()
            // }
            // // this.unviredSDK.dbExportWebData()






            let permitLogObj = new PERMIT_LOG();
            permitLogObj.PERMIT_NO = stakeHolder.PERMIT_NO;
            permitLogObj.OBJECT_STATUS = 1;
            permitLogObj.P_MODE = 'A';
            permitLogObj.FID = this.permit.LID;
            permitLogObj.APPR_TYPE = ''
            permitLogObj.LOG_NO = await this.findMaxLogNumberFromDb(
              this.permit.PERMIT_NO
            );
            permitLogObj.CREATED_ON = new Date().getTime();
            permitLogObj.CREATED_BY = `${this.userObject.USER_ID}`;




            permitLogObj.PERMIT_STATUS = 'ISSUED'
            permitLogObj.ACTION = 'CONFIRM'
            permitLogObj.APPROVAL = 'A'
            //  this.permit.APPROVAL_SUMMARY = 'A'
            permitLogObj.COMMENT = `${permitLogObj.CREATED_BY
              } create permit on ${moment().format('MMMM Do YYYY, h:mm:ss a')}`;


            console.log("the permit obj after action is ", this.permit, permitLogObj)
            let result = await this.unviredSDK.dbInsert('PERMIT_LOG', permitLogObj, false)

            if (result.type == ResultType.success) {


            } else {
              console.error("Error while approving the action")
            }




              
              // this.stakeActionStatusArray.push({ROW_ID: stakeHolder.ROW_ID , status: 'Executed' , comment: ''  , stakeData: stakeHolder})
            } else if(status == 'Confirmed'){
              const existingIndex = this.stakeActionStatusArray.findIndex(item => 
                item.ROW_ID === stakeHolder.ROW_ID
              );
              
              if (existingIndex === -1) {
                if(status == 'Confirmed'){
                  this.stakeActionStatusArray.push({ROW_ID: stakeHolder.ROW_ID , status: 'Confirmed' , comment: ''  , stakeData: stakeHolder})
                  this.hideConfirmButton(stakeHolder.ROW_ID)
                }
              }else {
                // Object exists, update its status
                this.stakeActionStatusArray[existingIndex].status = 'Confirmed';
                this.stakeActionStatusArray[existingIndex].comment = '';
              }


              
            }else if(status == 'Closed'){
              const existingIndex = this.stakeActionStatusArray.findIndex(item => 
                item.ROW_ID === stakeHolder.ROW_ID
              );
              
              if (existingIndex === -1) {
                if(status == 'Closed'){
                  this.stakeActionStatusArray.push({ROW_ID: stakeHolder.ROW_ID , status: 'Closed' , comment: ''  , stakeData: stakeHolder})
                  this.hideCloseButton(stakeHolder.ROW_ID)
                }
              }else {
                // Object exists, update its status
                this.stakeActionStatusArray[existingIndex].status = 'Closed';
                this.stakeActionStatusArray[existingIndex].comment = '';
              }


              
            }
        
        
 
      
          
        }



      } else {
        this.dataService.showAlertMessage(
          'Alert',
          'You dont have access for this approval'
        );
      }


    } else {
      this.dataService.showAlertMessage(
        'Alert',
        'Please add user for the partner'
      );
    }

  }

  hidePerformButton(ROW_ID: any): boolean {
    return this.stakeActionStatusArray.some(item => item.ROW_ID === ROW_ID && item.status === 'Executed') ||
      this.stakeHoldersList.some(s => s.ROLE === 'EXECUTE' && s.PROCESSED_ON != null && s.ROW_ID === ROW_ID);
  }

  checkReviewed() {

    const reviewStakeholders = this.stakeHoldersList.filter(s => s.ROLE === 'REVIEW' && s.APPR_TYPE == null);
    const allProcessedAndApproved = reviewStakeholders.length > 0 &&
      reviewStakeholders.every(s => s.PROCESSED_ON != null && s.APPROVAL == 'A');

    if (allProcessedAndApproved) {
      return true;
    } else if (this.stakeActionStatusArray.length > 0 && this.stakeActionStatusArray.every(item => item.stakeData.ROLE === 'REVIEW' && item.stakeData.APPR_TYPE == null && item.status === 'Approved')) {
      return true;
    } else {
      return false;
    }
  }


  hideConfirmButton(ROW_ID: any): boolean {
  if (this.stakeActionStatusArray.some(item => item.ROW_ID === ROW_ID && item.status === 'Confirmed')) {
    return true;
  } else if (this.stakeHoldersList.some(s => s.ROLE === 'APPROVE' && s.ROW_ID === ROW_ID && s.PROCESSED_ON != null)) {
    return true;
  } else if (
    this.checkReviewed() &&
    this.stakeHoldersList.some(s => s.ROLE === 'APPROVE' && s.ROW_ID === ROW_ID && s.PROCESSED_ON == undefined)
  ) {
    return false;
  }

  return true;
}

    hideCloseButton(ROW_ID: any): boolean {

   const executeStakeholders = this.stakeHoldersList.filter(s => s.ROLE === 'EXECUTE' && s.APPR_TYPE == null);
   
  const allProcessedAndExecuted = executeStakeholders.length > 0 &&
    executeStakeholders.every(s => s.PROCESSED_ON != null);

     if(this.stakeActionStatusArray.some(item => item.ROW_ID === ROW_ID && item.status === 'Closed')) {
      return true
    } else if(this.stakeHoldersList.some(s => s.ROLE === 'CLOSE' && s.ROW_ID === ROW_ID && s.PROCESSED_ON != null)) {
      return true;
    }
  // 
    else if (this.stakeHoldersList.some(s => s.ROLE === 'CLOSE' && s.ROW_ID === ROW_ID && s.PROCESSED_ON == undefined)  && ( allProcessedAndExecuted)  ) {
      return false;
    } else {
      return true;
    }
    
  
  }





  async filterDataForPartners() {

    this.isPartnersLoading = true;
    await this.getAllUsers()
    let userapprovals = await this.unviredSDK.dbSelect('USER_APPROVAL_TYPE', ``);

    const reviewedUserIds = new Set<string>();
    let approvers = [];
    let apprUsers = [];

    // Use map to transform all data first before applying it to the UI
    await Promise.all(
      this.stakeHoldersList.map(async (stake) => {
        switch (stake.ROLE) {
          case 'REVIEW':
            if (!stake.APPR_TYPE && !stake.CONTEXT) {
              stake.users = this.filteredUsers[1];
            } else if (stake.APPR_TYPE) {
              this.filteredUsers.forEach((users) => {
                users.forEach((user) => {
                  if (user.REVIEW === "true") {
                    reviewedUserIds.add(user.USER_ID);
                  }
                });
              });

              let reviewUser = userapprovals.data.filter(approval => reviewedUserIds.has(approval.USER_ID));

              if (reviewUser.length > 0) {
                reviewUser.forEach((rUser) => {
                  if (rUser.APPR_TYPE === stake.APPR_TYPE) {
                    apprUsers.push(rUser);
                  }
                });
              }

              stake.users = apprUsers.flatMap((user) =>
                stake.users.filter((u) => u.USER_ID === user.USER_ID && stake.APPR_TYPE === user.APPR_TYPE)
              );

              stake.actionButtonStatus = stake.APPROVAL === 'A' ? 'Approved' : stake.APPROVAL === 'R' ? 'Rejected' : '';
            }
            break;

          case 'APPROVE':
            if (!stake.APPR_TYPE && !stake.CONTEXT) {
              stake.users = this.filteredUsers.flatMap(users =>
                users.filter(user => user.APPROVE === "true")
              );
            }
            break;

          case 'EXECUTE':
            if (!stake.APPR_TYPE && !stake.CONTEXT) {
              stake.users = this.filteredUsers.flatMap(users =>
                users.filter(user => user.EXECUTE === "true")
              );
            }
            stake.actionButtonStatus = stake.APPROVAL === 'A' && stake.PROCESSED_ON != null ? 'Executed' : '';
            break;
        }

        if (stake.ROLE === 'REVIEW') {
          stake.actionButtonStatus = stake.APPROVAL === 'A' ? 'Approved' : stake.APPROVAL === 'R' ? 'Rejected' : '';
        }
        if (stake.ROLE === 'EXECUTE') {
          stake.actionButtonStatus = stake.CONTEXT === 'FORM_COMPLETED' && stake.PROCESSED_ON != null ? 'Executed' : 'Not executed';
        }
      })
    );

    // Ensure changes are applied in one go to avoid UI flickering
    this.stakeHoldersList = [...this.stakeHoldersList];
    console.log("the stakes after filter", this.stakeHoldersList)

    this.isPartnersLoading = false;
  }



  canShowBothButtons(stakeHolder: any): boolean {
    if (stakeHolder.ROLE == 'REVIEW' && stakeHolder.APPR_TYPE == null) {
      return this.stakeHoldersList
        .filter(sh => sh.ROLE === 'REVIEW' && sh.APPR_TYPE != null)
        .every(sh => sh.actionButtonStatus === 'Approved');
    }
    return false; // Prevents unintended button display
  }



  canShowApproveAndRejectButtonsForReview(stakeHolder: any): boolean {
    // console.log("canShowApproveAndRejectButtonsForReview", this.stakeHoldersList , this.stakeActionStatusArray)
    this.cdr.markForCheck();
    // console.log("canShowApproveAndRejectButtonsForReview", stakeHolder);
    const filteredStakeHolders = this.stakeHoldersList.filter(
      sh => sh.ROLE === 'REVIEW' && sh.APPR_TYPE !== null
    );

    // console.log("filteredStakeHolders" , filteredStakeHolders)

    if (stakeHolder.ROLE == 'REVIEW' && stakeHolder.APPR_TYPE == null) {
      const allApproved = filteredStakeHolders.every(sh => sh.APPROVAL === 'A');
      const anyRejected = filteredStakeHolders.some(sh => sh.APPROVAL === 'R');
      const allUndefined = filteredStakeHolders.every(sh => sh.APPROVAL === 'O');
      const anyApproved = filteredStakeHolders.some(sh => sh.APPROVAL === 'A');
      const someUndefined = filteredStakeHolders.some(sh => sh.APPROVAL === 'O');


      if (anyRejected) {
        this.isHideNewStakeHolderButton = true
        return false; // No buttons should be displayed
      }

      if (allApproved) {
        return true; // Show both Approve and Reject buttons
      }

      if (allUndefined) {
        return true; // Show only Reject button
      }

      if (anyApproved && someUndefined) {
        return true; // Show only Reject button
      }
    }

    return false;
  }

  shouldShowApproveButton(stakeHolder: any): boolean {
    let reviewsArray = this.stakeHoldersList.filter(sh => sh.ROLE === 'REVIEW' && sh.APPR_TYPE !== null && sh.PROCESSED_ON == null);
    // console.log("reviews array" , reviewsArray)

    if (stakeHolder.ROLE == 'REVIEW' && stakeHolder.APPR_TYPE == null) {
      const filteredStakeHolders2 = this.stakeHoldersList.filter(
        sh => sh.ROLE === 'REVIEW'
      );
      const allApproved2 = filteredStakeHolders2.every(sh => sh.APPROVAL === 'A');
      const allApproved1 = reviewsArray.every(sh => sh.APPROVAL === 'A');
      let allApproved = reviewsArray.length;
      if (this.stakeActionStatusArray.length > 0 && this.stakeActionStatusArray.length == allApproved && this.stakeActionStatusArray.every(ele => ele.status == 'Approved')) {
        //  /    console.log(allApproved);

        return true;
      } else if (this.stakeActionStatusArray.length == 0) {
        const filteredStakeHolders = this.stakeHoldersList.filter(
          sh => sh.ROLE === 'REVIEW' && sh.APPR_TYPE !== null
        );

        // console.log("filteredStakeHolders" , filteredStakeHolders)

        // if () {
        //   // const allApproved = filteredStakeHolders.every(sh => sh.APPROVAL === 'A');
        //   // if(allApproved){
        //   //   return true;
        //   // } else {
        //   //   return false;
        //   // }
        // }

        if (stakeHolder.ROLE == 'REVIEW' && stakeHolder.APPR_TYPE == null && this.stakeActionStatusArray.length == 0) {

          if (allApproved1 && allApproved2) {
            return false
          } else if (allApproved1 && !allApproved2) {
            return true
          }
        }
      }

      else {
        return false;
      }
    }

  }

  shouldShowRejectButton(stakeHolder: any): boolean {

    let data = this.stakeActionStatusArray.filter((d) => d.ROW_ID == stakeHolder.ROW_ID);
    const filteredStakeHolders = this.stakeHoldersList.filter(
      sh => sh.ROLE === 'REVIEW' && sh.APPR_TYPE !== null
    );
    const allApproved = filteredStakeHolders.every(sh => sh.APPROVAL === 'A');
    const filteredStakeHolders2 = this.stakeHoldersList.filter(
      sh => sh.ROLE === 'REVIEW'
    );
    const allApproved2 = filteredStakeHolders2.every(sh => sh.APPROVAL === 'A');
    if (this.stakeActionStatusArray.length == 0) {

      if (allApproved && allApproved2) {
        return false
      } else if (allApproved && !allApproved2) {
        return true
      }
    }

    if (stakeHolder.APPR_TYPE != null && stakeHolder.ROLE == 'REVIEW') {

      if (data.length > 0) {

        let rej = this.stakeActionStatusArray.find(ele => ele.status == 'Rejected')
        if (rej) {

          return false
        }



        // return true
      } else {
        let rej = this.stakeActionStatusArray.find(ele => ele.status == 'Rejected')

        if (rej) {

          return false
        } else {
          return true;
        }

      }
    } else {
      if (data.length == 0 && this.stakeActionStatusArray.length == 0) {

        if (allApproved) {
          return false;
        } else {
          return true
        }

      } else if (data.length > 0 && data[0].ROW_ID == 2) {

        let reject = this.stakeActionStatusArray.some(ele => ele.ROW_ID == 2 && ele.status == 'Rejected')

        if (reject) {
          return false
        } else {
          if (this.stakeActionStatusArray.length > 0 && this.stakeActionStatusArray.some(ele => ele.status == 'Rejected')) {
            return true
          } else {
            if (this.stakeActionStatusArray.length > 0 && this.stakeActionStatusArray.every(ele => ele.status == 'Approved')) {
              return false
            } else {
              return true
            }

          }
        }


      } else if (data.length == 0 && this.stakeActionStatusArray.length > 0) {
        if (this.stakeActionStatusArray.length > 0 && this.stakeActionStatusArray.some(ele => ele.status == 'Rejected')) {
          return false
        }
        else {
          return true
        }
      }
    }


  }





  checkUserForButtons(stakeHolder): boolean {
    // console.log("the user exists in the array" , stakeHolder.ROW_ID)
    // console.log("stakeholder data ", stakeHolder , this.stakeActionStatusArray)
    // console.log("checkUserForButtons" , this.stakeActionStatusArray , stakeHolder)


    if (stakeHolder.ROLE == 'REVIEW' && stakeHolder.APPR_TYPE == null && stakeHolder.USER_ID == this.userObject.USER_ID) {
      return this.canShowApproveAndRejectButtonsForReview(stakeHolder)

    } else {

      if (stakeHolder.USER_ID != null && stakeHolder.USER_ID == this.userObject.USER_ID) {
        // console.log()
        if (this.showFillFormButton == true) {
          return false;
        } else {

          return true;
        }
        // if(this.showFillFormButton == false){
        //   return true;
        // }else{
        //   return false
        // }

      } else {
        return false;
      }
    }

  }




  canShowApproveAndRejectButtons(stakeHolder): boolean {
    // Get all reviewers except ROW_ID == 2
    const filteredStakeHolders = this.stakeHoldersList.filter(
      sh => sh.ROLE === 'REVIEW' && sh.APPR_TYPE !== null
    );

    // If the current row is ROW_ID == 2, check if ALL other reviewers have 'Approved' status
    if (stakeHolder.ROLE == 'REVIEW' && stakeHolder.APPR_TYPE == null) {
      let data = filteredStakeHolders.length > 0 &&
        filteredStakeHolders.every(sh => sh.actionButtonStatus === 'Approved' && sh.ROLE == 'REVIEW');
      console.log("data is in approve", data)
      return data
    }

    let data2 = filteredStakeHolders.length > 0 &&
      filteredStakeHolders.every(sh => sh.actionButtonStatus === 'Approved' || sh.actionButtonStatus === null);
    console.log("data2 is ", data2)
    return data2
  }




  canShowRejectButton(stakeHolder): boolean {
    if (stakeHolder.ROLE == 'REVIEW' && stakeHolder.APPR_TYPE == null) {
      return true; // Always show Reject button for ROW_ID == 2
    } else {
      // For other rows, check if any reviewer has Rejected status

      let data = this.stakeHoldersList.some(sh =>
        sh.ROLE === 'REVIEW' && sh.APPR_TYPE !== null && sh.actionButtonStatus === 'Rejected'
      );

      return data
    }


  }







  hasReviewersWithNullUserId(): boolean {
    return this.stakeHoldersList.some(
      stakeHolder =>
        stakeHolder.ROLE === 'REVIEW' &&
        stakeHolder.APPR_TYPE !== null &&
        stakeHolder.USER_ID === null ||
        stakeHolder.ROLE === 'REVIEW' && stakeHolder.APPR_TYPE !== null
    );
  }


  async permitAction(
    status: any,
    permitHeader: any,
    isRequriedComment: boolean,
    permitStakeholder?: any
  ) {
    console.log("permit action in ", permitHeader, permitStakeholder)
    switch (status) {
      case PermitStatus.OPEN:
        let res = await this.addPermitUserAssignPopup(permitHeader);
        if (res) {
          await this.submitPermit(status, permitHeader, res);

        }
        break;
      case PermitStatus.IN_REVIEW:
        console.log("permit header in review is ", permitHeader, permitStakeholder)

        if (this.isPermitIsExpired(permitHeader)) {
          await this.showAlert(
            this.translate.instant('Alert'),
            'Permit can only be approved before expiry'
          );
        }
        else {
          console.log("SUBMIT PERMIT IN_REVIEW")
          await this.submitPermit(status, permitHeader);

        }
        break;
      case PermitStatus.REVISE:

        const alert = await this.alertController.create({
          header: 'Alert',
          message: 'Proceeding with revise will require all approvals, reviews & execution to be redone. Are you sure you want to continue?',
          buttons: [
            {
              text: 'Cancel',
              role: 'cancel',
              handler: () => {
                console.log('Revise action canceled');
                return; // Exit without proceeding
              },
            },
            {
              text: 'Yes',
              handler: async () => {
                console.log('Revise action confirmed');
                this.alertController.dismiss();
                if (this.isPermitIsExpired(permitHeader)) {
                  if (
                    permitHeader.IS_EXTENDED == true ||
                    permitHeader.IS_EXTENDED == 'true'
                  ) {
                    await this.showAlert(
                      this.translate.instant('Alert'),
                      'The permit has expired after being extended and is no longer eligible for revision.'
                    );
                  } else {
                    await this.showAlert(
                      this.translate.instant('Alert'),
                      'Permit can only be revised before expiry'
                    );
                  }
                } else {
                  this.dataService.isRevised = true;


                  // Call submitPermit if confirmed
                  let permitformUpdateQuery = `UPDATE PERMIT_FORM SET P_MODE = 'M', OBJECT_STATUS = 2, DATA = '{}' WHERE PERMIT_NO = '${this.permit.PERMIT_NO}'`;
                  let permitformUpdateResult = await this.unviredSDK.dbExecuteStatement(
                    permitformUpdateQuery
                  );
                  if (permitformUpdateResult.type == ResultType.success) {

                    console.log("permitformUpdateResult" + permitformUpdateResult);
                  } else {
                    console.log("FAILURE - update permit form details in DB")
                  }
                  let res = await this.addPermitUserAssignPopup(permitHeader);
                  if (res) {
                    await this.submitPermit(status, permitHeader, res);

                  }
                  // await this.submitPermit(status, permitHeader);
                }
              },
            },
          ],
        });

        await alert.present();
        break;
      case PermitStatus.APPROVED:
        if (this.isPermitIsExpired(permitHeader)) {
          await this.showAlert(
            this.translate.instant('Alert'),
            'Permit can only be issued on the date of execution and before expiry'
          );
        } else {
          await this.submitPermit(status, permitHeader);

        }
        break;
      default:
        await this.submitPermit(status, permitHeader);

        break;
    }
  }


  async checkIfFormData(status: string, index: number, stakeholder: any) {
    let val = await returnTempData();
    await this.prepareFormResourcesAndSetFormData()
    let appr_exists = this.searchForApprovalType(this.formData, stakeholder.APPR_TYPE);
    console.log("the appr exists is " , appr_exists)
    console.log(this.formData , "the form data is " , this.formData , this.subData)
    if(this.isFormFound == false){
      console.log('action button called here once')
      await this.actionButtons(status, index, stakeholder);
    } else {
      this.subData.RoleName = stakeholder.ROLE
      this.subData.ApprovalType = stakeholder.APPR_TYPE
    }

    if (this.formData && appr_exists) {
      const modal = await this.modalController.create({
        component: EditFormComponent,
        id: 'newFormDisplayPopup',
        cssClass: this.isMobile ? '' : 'newFormDisplayPopup',
        mode: 'ios',
        componentProps: {
          // userObject: this.userObject,
          // formData: this.formData,
          // subData: this.subData,
          // permit: this.permit,
          selectedStakeholder: stakeholder,
          actionStatus: status,
          // formReadOnlyFlag: this.formReadOnlyFlag,
          // translateJson: this.translateJson,
          // attributesJson: this.attributesJson
        },
        backdropDismiss: false
      });

      await modal.present();

      const { data } = await modal.onWillDismiss();
      if (data?.saved) {
        await this.actionButtons(status, index, stakeholder);
      }
    } else {
        console.log('action button called here twice')
      await this.actionButtons(status, index, stakeholder);
    }

  }















  async addPermitUserAssignPopup(permit: PERMIT_HEADER) {
    const modal = await this.modalController.create({
      component: AddPermitRoleComponent,
      mode: 'ios',
      componentProps: {
        permit: permit,
      },
    });

    modal.present();

    const { data } = await modal.onWillDismiss();
    return data;
  }

  async submitPermit(status: any, permitHeader: any, selectedUser?: any) {
    if (status && status != null && status != '') {
      let isProceedProcess: boolean = true;

      if (isProceedProcess) {

        if (status === 'CLOSED') {
          let permitformUpdateQuery = `UPDATE PERMIT_FORM SET COMPLETED = 'X', PARTIAL_FLAG = '' WHERE PERMIT_NO = '${permitHeader.PERMIT_NO}'`;
          let permitformUpdateResult = await this.unviredSDK.dbExecuteStatement(
            permitformUpdateQuery
          );
          if (permitformUpdateResult.type == ResultType.success) {
            console.log("permitformUpdateResult" + permitformUpdateResult);
          } else {
            console.log("FAILURE - update permit form details in DB")
          }
        }
        let permitformQuery = `SELECT * FROM PERMIT_FORM WHERE PERMIT_NO = '${permitHeader.PERMIT_NO}'`;
        let permitformResult = await this.unviredSDK.dbExecuteStatement(
          permitformQuery
        );
        if (permitformResult.type == ResultType.success) {
          console.log("permitformResult" + permitformResult);
          if (permitformResult?.data && permitformResult?.data?.length > 0 && permitformResult.data[0].DATA) {
            permitHeader.COMMENTS = JSON.parse(permitformResult.data[0].DATA).permitComments ? JSON.parse(permitformResult.data[0].DATA).permitComments : permitHeader.COMMENTS;
          }
        } else {
          console.log("FAILURE - fetch permit form details from DB")
        }
        let permitUpdateQuery = `UPDATE PERMIT_HEADER SET P_MODE = 'M', OBJECT_STATUS = 2 WHERE PERMIT_NO = '${permitHeader.PERMIT_NO}'`;
        let permitUpdateQueryResult = await this.unviredSDK.dbExecuteStatement(
          permitUpdateQuery
        );
        if (permitUpdateQueryResult.type == ResultType.success) {
          permitHeader.P_MODE = 'M';
          permitHeader.OBJECT_STATUS = 2;
        }

        let permitStatusLocal: string = '';
        let comment: string = '';
        let user = this.userObject?.FIRST_NAME + ' ' + this.userObject?.LAST_NAME;
        switch (status) {
          case PermitStatus.OPEN:
            permitStatusLocal = PermitStatus.IN_REVIEW;
            comment = `${user} ${this.translate.instant(
              'submit for the review process on'
            )} ${moment(new Date()).format('DD MMMM YYYY hh:mm A')}`;

            let stakeHolder = new PERMIT_STAKEHOLDER();
            stakeHolder.P_MODE = 'A';
            stakeHolder.USER_ID = selectedUser.USER_ID;
            stakeHolder.ROLE = 'REVIEW';
            stakeHolder.PERMIT_NO = permitHeader.PERMIT_NO;
            stakeHolder.SYNC_STATUS = 0;
            stakeHolder.OBJECT_STATUS = 1;
            stakeHolder.LID = this.unviredSDK.guid().replace(/-/g, '');
            stakeHolder.FID = permitHeader.LID;
            stakeHolder.COMMENT = '';
            stakeHolder.AGENT_ID = permitHeader.AGENT_ID_INT;
            stakeHolder.ROW_ID = await this.findMaxRowNumberFromDb(
              permitHeader.PERMIT_NO
            );

            await this.unviredSDK.dbInsert(
              'PERMIT_STAKEHOLDER',
              stakeHolder,
              false
            );

            //Setting permit log object status to 0 to send only one permit log to server.
            let permitLogUpdateQuery = `UPDATE PERMIT_LOG SET OBJECT_STATUS = 0 , ACTION = 'TRSNSITION' WHERE PERMIT_NO = '${permitHeader.PERMIT_NO}' AND PERMIT_STATUS != '${permitStatusLocal}' AND PERMIT_STATUS NOT NULL`;
            await this.unviredSDK.dbExecuteStatement(permitLogUpdateQuery);

            console.log("created new permit log")
            // Adding new permit log.
            let i = 0;
            if (i == 0) {
              i = i + 1;
              let permitLog = new PERMIT_LOG();
              permitLog.LOG_NO = await this.findMaxLogNumberFromDb(
                permitHeader.PERMIT_NO
              );

              permitLog.LID = this.unviredSDK.guid().replace(/-/g, '');
              permitLog.SYNC_STATUS = 0;
              permitLog.OBJECT_STATUS = 1;
              permitLog.CREATED_ON = moment().valueOf();
              permitLog.P_MODE = 'A';
              permitLog.PERMIT_NO = permitHeader.PERMIT_NO;
              permitLog.FID = permitHeader.LID;
              permitLog.PERMIT_STATUS = permitStatusLocal;
              permitLog.COMMENT = comment;
              permitLog.CREATED_BY = user;
              permitLog.ACTION = 'TRANSITION';
              let permitLogInsertResult = await this.unviredSDK.dbInsert(
                'PERMIT_LOG',
                permitLog,
                false
              );
            }
            await this.regularSubmitPermit(permitHeader);
            break;

          case PermitStatus.REVISE:

            permitStatusLocal = PermitStatus.IN_REVIEW;
            comment = `${user} ${this.translate.instant(
              'Revise Permit for the review process on'
            )} ${moment(new Date()).format('DD MMMM YYYY hh:mm A')}`;

            let newstakeHolder = new PERMIT_STAKEHOLDER();
            newstakeHolder.P_MODE = 'A';
            newstakeHolder.USER_ID = selectedUser.USER_ID;
            newstakeHolder.ROLE = 'REVIEW';
            newstakeHolder.PERMIT_NO = permitHeader.PERMIT_NO;
            newstakeHolder.SYNC_STATUS = 0;
            newstakeHolder.OBJECT_STATUS = 1;
            newstakeHolder.LID = this.unviredSDK.guid().replace(/-/g, '');
            newstakeHolder.FID = permitHeader.LID;
            newstakeHolder.COMMENT = '';
            newstakeHolder.AGENT_ID = permitHeader.AGENT_ID_INT;
            newstakeHolder.IS_ACTIVE = null;
            newstakeHolder.ROW_ID = await this.findMaxRowNumberFromDb(
              permitHeader.PERMIT_NO
            );

            console.log("new stake holder is ", newstakeHolder)
            let permitStakeInsertResult = await this.unviredSDK.dbInsert(
              'PERMIT_STAKEHOLDER',
              newstakeHolder,
              false
            );
            // this.unviredSDK.dbExportWebData();
            //Setting permit log object status to 0 to send only one permit log to server.
            // let permitLogUpdateQuery1 = `UPDATE PERMIT_LOG SET OBJECT_STATUS = 0 WHERE PERMIT_NO = '${permitHeader.PERMIT_NO}' AND PERMIT_STATUS != '${permitStatusLocal}' AND PERMIT_STATUS NOT NULL`;
            // await this.unviredSDK.dbExecuteStatement(permitLogUpdateQuery1);

            // Adding new permit log.
            // let permitLog = new PERMIT_LOG();
            // permitLog.LOG_NO = await this.findMaxLogNumberFromDb(
            //   permitHeader.PERMIT_NO
            // );

            // permitLog.LID = this.unviredSDK.guid().replace(/-/g, '');
            // permitLog.SYNC_STATUS = 0;
            // permitLog.OBJECT_STATUS = 1;
            // permitLog.CREATED_ON = moment().valueOf();
            // permitLog.P_MODE = 'A';
            // permitLog.PERMIT_NO = permitHeader.PERMIT_NO;
            // permitLog.FID = permitHeader.LID;
            // permitLog.PERMIT_STATUS = permitStatusLocal;
            // permitLog.COMMENT = comment;
            // permitLog.CREATED_BY = user;
            // let permitLogInsertResult = await this.unviredSDK.dbInsert(
            //   'PERMIT_LOG',
            //   permitLog,
            //   false
            // );


            //Setting permit log object status to 0 to send only one permit log to server.
            let permitLogUpdateQuery1 = `UPDATE PERMIT_LOG SET OBJECT_STATUS = 0 , ACTION = 'TRANSITION' WHERE PERMIT_NO = '${permitHeader.PERMIT_NO}' AND PERMIT_STATUS != '${permitStatusLocal}' AND PERMIT_STATUS NOT NULL`;
            await this.unviredSDK.dbExecuteStatement(permitLogUpdateQuery1);

            // Adding new permit log.
            let permitLog = new PERMIT_LOG();
            permitLog.LOG_NO = await this.findMaxLogNumberFromDb(
              permitHeader.PERMIT_NO
            );

            permitLog.LID = this.unviredSDK.guid().replace(/-/g, '');
            permitLog.SYNC_STATUS = 0;
            permitLog.OBJECT_STATUS = 1;
            permitLog.CREATED_ON = moment().valueOf();
            permitLog.P_MODE = 'A';
            permitLog.PERMIT_NO = permitHeader.PERMIT_NO;
            permitLog.FID = permitHeader.LID;
            permitLog.PERMIT_STATUS = permitStatusLocal;
            permitLog.COMMENT = comment;
            permitLog.CREATED_BY = user;
            permitLog.ACTION = 'TRANSITION';
            let permitLogInsertResult = await this.unviredSDK.dbInsert(
              'PERMIT_LOG',
              permitLog,
              false
            );
            this.dataService.isRevised = false;
            await this.regularSubmitPermit(permitHeader);
            break;

          case PermitStatus.IN_REVIEW:
            console.log("in_review permit")
            let query = `SELECT * FROM PERMIT_LOG WHERE PERMIT_NO= '${permitHeader.PERMIT_NO}' AND PERMIT_STATUS='APPROVED' AND P_MODE='A'`;
            let countResult = await this.unviredSDK.dbExecuteStatement(query);
            if (countResult && countResult.data.length == 0) {
              let comment = `${user} send for APPROVE ${moment(
                new Date()
              ).format('DD MMMM YYYY hh:mm A')}`;

              console.log("created new permit log")
              let permitLog = new PERMIT_LOG();
              permitLog.LOG_NO = await this.findMaxLogNumberFromDb(
                permitHeader.PERMIT_NO
              );

              permitLog.LID = this.unviredSDK.guid().replace(/-/g, '');
              permitLog.SYNC_STATUS = 0;
              permitLog.OBJECT_STATUS = 1;
              permitLog.CREATED_ON = moment().valueOf();
              permitLog.P_MODE = 'A';
              permitLog.PERMIT_NO = permitHeader.PERMIT_NO;
              permitLog.FID = permitHeader.LID;
              permitLog.PERMIT_STATUS = 'APPROVED';
              permitLog.COMMENT = comment;
              permitLog.CREATED_BY = user;
              permitLog.ACTION = 'TRANSITION';
              await this.unviredSDK.dbInsert('PERMIT_LOG', permitLog, false);

              await this.regularSubmitPermit(permitHeader);
            } else {
              await this.regularSubmitPermit(permitHeader);
            }
            break;

          case PermitStatus.APPROVED:


            // let inspectionLogUpdateQuery = `UPDATE PERMIT_STAKEHOLDER SET OBJECT_STATUS = 2, P_MODE = 'M'  WHERE PERMIT_NO = '${permitHeader.PERMIT_NO}' AND ROW_ID = '${stakeHolder.ROW_ID}'`;
            // let stakeHolderUpdateQueryResult = await this.unviredSDK.dbExecuteStatement(
            //   inspectionLogUpdateQuery
            // );
            // if (
            //   stakeHolderUpdateQueryResult &&
            //   stakeHolderUpdateQueryResult.data
            // ) {
            //   console.log("stakeHolderUpdateQueryResult" , stakeHolderUpdateQueryResult.data)
            //   // this.unviredSDK.dbExportWebData()
            // }
            // // this.unviredSDK.dbExportWebData()

            // let permitLogObj =  new PERMIT_LOG();
            // permitLogObj.PERMIT_NO = stakeHolder.PERMIT_NO;
            // permitLogObj.OBJECT_STATUS = 1;
            // permitLogObj.P_MODE = 'A';
            // permitLogObj.FID = this.permit.LID;
            // permitLogObj.APPR_TYPE = ''
            // permitLogObj.LOG_NO = await this.findMaxLogNumberFromDb(
            //   this.permit.PERMIT_NO
            // );
            // permitLogObj.CREATED_ON = new Date().getTime();
            // permitLogObj.CREATED_BY = `${this.userObject.USER_ID}`;




            //  permitLogObj.PERMIT_STATUS = 'ISSUED'
            //  permitLogObj.ACTION = 'CONFIRM'
            //  permitLogObj.APPROVAL = 'A'
            // //  this.permit.APPROVAL_SUMMARY = 'A'
            //  permitLogObj.COMMENT = `${permitLogObj.CREATED_BY
            //  } create permit on ${moment().format('MMMM Do YYYY, h:mm:ss a')}`;


            //  console.log("the permit obj after action is " , this.permit , permitLogObj)
            //  let result = await this.unviredSDK.dbInsert('PERMIT_LOG', permitLogObj , false )

            //  if (result.type == ResultType.success) {
            //   this.unviredSDK.dbExportWebData()
            //   // await this.regularSubmitPermit(permitHeader);

            //  } else{
            //    console.error("Error while approving the action")
            //  }






            let approveQuery = `SELECT * FROM PERMIT_LOG WHERE PERMIT_NO= '${permitHeader.PERMIT_NO}' AND PERMIT_STATUS='ISSUED' AND P_MODE='A'`;
            let approveQueryResult = await this.unviredSDK.dbExecuteStatement(
              approveQuery
            );
            if (approveQueryResult && approveQueryResult?.data?.length == 0) {
              let comment = `${user} send for ISSUE ${moment(new Date()).format(
                'DD MMMM YYYY hh:mm A'
              )}`;
              console.log("created new permit log")
              let permitLog = new PERMIT_LOG();
              permitLog.LOG_NO = await this.findMaxLogNumberFromDb(
                permitHeader.PERMIT_NO
              );

              permitLog.LID = this.unviredSDK.guid().replace(/-/g, '');
              permitLog.SYNC_STATUS = 0;
              permitLog.OBJECT_STATUS = 1;
              permitLog.CREATED_ON = moment().valueOf();
              permitLog.P_MODE = 'A';
              permitLog.PERMIT_NO = permitHeader.PERMIT_NO;
              permitLog.FID = permitHeader.LID;
              permitLog.PERMIT_STATUS = 'ISSUED';
              permitLog.COMMENT = comment;
              permitLog.CREATED_BY = user;
              permitLog.ACTION = 'TRANSITION';
              await this.unviredSDK.dbInsert('PERMIT_LOG', permitLog, false);

              await this.regularSubmitPermit(permitHeader);
            } else {
              await this.regularSubmitPermit(permitHeader);
            }
            break;

          case PermitStatus.CLOSED:
            let closedQuery = `SELECT * FROM PERMIT_LOG WHERE PERMIT_NO= '${permitHeader.PERMIT_NO}' AND PERMIT_STATUS='CLOSED' AND P_MODE='A'`;
            let closedQueryResult = await this.unviredSDK.dbExecuteStatement(
              closedQuery
            );
            if (closedQueryResult && closedQueryResult?.data?.length == 0) {
              let comment = `${user} send for CLOSED ${moment(
                new Date()
              ).format('DD MMMM YYYY hh:mm A')}`;
              console.log("created new permit log")
              let permitLog = new PERMIT_LOG();
              permitLog.LOG_NO = await this.findMaxLogNumberFromDb(
                permitHeader.PERMIT_NO
              );

              permitLog.LID = this.unviredSDK.guid().replace(/-/g, '');
              permitLog.SYNC_STATUS = 0;
              permitLog.OBJECT_STATUS = 1;
              permitLog.CREATED_ON = moment().valueOf();
              permitLog.P_MODE = 'A';
              permitLog.PERMIT_NO = permitHeader.PERMIT_NO;
              permitLog.FID = permitHeader.LID;
              permitLog.PERMIT_STATUS = 'CLOSED';
              permitLog.COMMENT = comment;
              permitLog.CREATED_BY = user;
              permitLog.ACTION = 'TRANSITION';
              await this.unviredSDK.dbInsert('PERMIT_LOG', permitLog, false);

              await this.regularSubmitPermit(permitHeader);
            } else {
              await this.regularSubmitPermit(permitHeader);
            }
            break;
          default:
            await this.regularSubmitPermit(permitHeader);
            break;
        }
      }
    }
  }

  async regularSubmitPermit(permitHeader: any) {
    let isAvailableButton = permitHeader.hasOwnProperty('isShowDetailsButton');
    if (isAvailableButton) {
      delete permitHeader.isShowDetailsButton;
    }
    let isShowReviseButton = permitHeader.hasOwnProperty('isShowReviseButton');
    if (isShowReviseButton) {
      delete permitHeader.isShowReviseButton;
    }
    // Remove permitTypeInfo as it's only used for display purposes and should not be sent to server
    if (permitHeader.hasOwnProperty('permitTypeInfo')) {
      delete permitHeader.permitTypeInfo;
    }
    console.log("permit in modify permit details ", this.permit)
    // this.unviredSDK.dbExportWebData();
    let permitHeaderResponse: any = await this.dataService.modifyPermit(
      permitHeader
    );
    await this.displayPleaseWaitLoader();
    let infoMsg = this.dataService.handleInfoMessage(permitHeaderResponse);
    if (permitHeaderResponse.type == ResultType.success) {
      this.loadingController.dismiss();
      console.log("loader dismissed ")

      if (infoMsg && infoMsg?.length > 0) {
        await this.showAlert('Info', infoMsg);
      } else {
        this.ngZone.run(async () => {
          // this.router.navigate(['/permits'])
          // await this.downloadPermits(10, 0, true);
          // await this.fetchStatusBasedPermits(this.segmentValue);
        });
      }
      // await this.getAllStakeHolders();
      await this.save()
    } else {
      if (
        permitHeaderResponse.message &&
        permitHeaderResponse.message.length > 0
      ) {
        await this.showAlert(
          this.translate.instant('Error'),
          permitHeaderResponse.message
        );
      } else if (
        permitHeaderResponse.error &&
        permitHeaderResponse.error.length > 0
      ) {
        await this.showAlert(
          this.translate.instant('Info'),
          permitHeaderResponse.error
        );
      } else {
        await this.showAlert(
          this.translate.instant('Error'),
          this.translate.instant(
            'Error occured while updating permit, Please try again'
          )
        );
      }
    }
  }


  // Download permits from server.
  async downloadPermits(limit: number, offset: number, autoSave: boolean) {
    await this.displayPleaseWaitLoader();
    // this.permitList = [];
    // this.permitFilteredList = [];
    let deleteInspectionsQuery = `DELETE FROM PERMIT_HEADER`;
    await this.unviredSDK.dbExecuteStatement(deleteInspectionsQuery);
    let deletestakeHoldersQuery = `DELETE FROM PERMIT_STAKEHOLDER`;
    await this.unviredSDK.dbExecuteStatement(deletestakeHoldersQuery);
    let permitsResponse: any = await this.dataService.downloadPermits(
      limit,
      offset,
      autoSave
    );
    this.loadingController.dismiss();
    if (permitsResponse.type == ResultType.success) {

      if (permitsResponse?.data?.InfoMessage?.length > 0) {
        let msg = this.dataService.handleInfoMessage(permitsResponse);
        this.showAlert(this.translate.instant('Alert'), msg);
      } else {
        // await this.fetchStatusBasedPermits(this.segmentValue);
      }
    } else {
      this.dataService.displayErrorMessageDialog(permitsResponse.error);
    }
  }

  async findMaxRowNumberFromDb(permitNo: any) {
    let maxNumber = 1;
    let fetchMaxLogNumberQuery = `SELECT MAX(ROW_ID + 0) AS maxNumber FROM PERMIT_STAKEHOLDER WHERE PERMIT_NO = '${permitNo}';`;
    let maxLogNumberResult: any = await this.unviredSDK.dbExecuteStatement(
      fetchMaxLogNumberQuery
    );
    if (
      maxLogNumberResult &&
      maxLogNumberResult.data.length > 0 &&
      maxLogNumberResult.data[0].maxNumber
    ) {
      maxNumber = Number(maxLogNumberResult.data[0].maxNumber) + 1;
      console.log("maxLogNumberResult.data[0].maxNumber", maxLogNumberResult.data[0].maxNumber, maxNumber)
    }
    return maxNumber.toString();
  }


  async displayPleaseWaitLoader() {
    const loading = await this.loadingController.create({
      message: this.translate.instant('Please wait') + '...',
      backdropDismiss: false,
    });
    await loading.present();
  }


  isApproveButtonDisabled(requiredRole: string): boolean {
    const isDisabled = (this.stakeHoldersList && this.stakeHoldersList.some(s => s.ROLE == requiredRole))
    return isDisabled;
  }

  async isButtonDisabled(requiredRole?: string): Promise<boolean> {
    // console.log("isButtonDisabled", requiredRole);

    // Fetch user role data
    let userRoleData = await this.dataService.getData('USER_ROLE');
    // console.log(userRoleData);

    // Check if the required role exists and is set to 'true'
    if (requiredRole && userRoleData[0]?.[requiredRole] === 'true') {
      return false; // Button is enabled
    }

    return true; // Button is disabled
  }


  shouldShowApproveButtonInStake(): boolean {
    return this.permit.STATUS === 'IN_REVIEW' && this.stakeHoldersList.some(s => s.ROLE === 'EXECUTE') &&
      (this.stakeHoldersList.some(s => s.ROLE === 'REVIEW' && s.APPR_TYPE === null && s.APPROVAL === 'A') &&
        this.stakeActionStatusArray.some(item => item.ROLE == 'REVIEW' && item.APPR_TYPE == null && item.status === 'Approved'));
  }


  async addCommentsToReject(stakeHolder: any, addReason: boolean, index: number) {

    let rejData = this.stakeActionStatusArray.find((ele) => ele.ROW_ID == stakeHolder.ROW_ID)
    if (rejData) {
      stakeHolder['rejectReason'] = rejData.comment
    } else {
      stakeHolder['rejectReason'] = undefined
    }
    const modal = await this.modalController.create({
      component: RejectPermitComponent,
      id: 'rejectReason',
      cssClass: 'rejectReason',
      componentProps: {
        stakeData: JSON.parse(JSON.stringify(stakeHolder)),
        permit: this.permit,
        addReason: addReason
      }
    });

    modal.present();

    const { data } = await modal.onDidDismiss();


    if (data) {

      this.rejectedComment = data;

      stakeHolder.rejectReason = data;
      this.isHideNewStakeHolderButton = true;

      this.stakeHoldersList[index] = stakeHolder

      if (rejData) {
        for (let i = 0; i < this.stakeActionStatusArray.length; i++) {
          if (rejData.ROW_ID == this.stakeActionStatusArray[i].ROW_ID) {
            this.stakeActionStatusArray[i].comment = data;
          }
        }
      } else{
        this.stakeActionStatusArray.push({ROW_ID: stakeHolder.ROW_ID , status: 'Rejected', comment: data  , stakeData: stakeHolder})
        
      }
      return data;
    }
  }


  getActionStatus(stake: any) {

    let data = this.stakeActionStatusArray.find((s) => s.ROW_ID == stake.ROW_ID);
    if (data) {

      return data.status
    }
  }

  getChipColor(status: string): string {
    switch (status) {
      case 'Approved':
        return 'success';
      case 'Rejected':
        return 'danger';
      case 'Executed':
      case 'Confirmed':
        return 'primary';

      case 'Closed':
        return 'primary';
      default:
        return 'default';
    }
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'OPEN':
        return 'primary';
      case 'IN_REVIEW':
        return 'warning';
      case 'APPROVED':
        return 'success';
      case 'ISSUED':
        return 'success';
      case 'CLOSED':
        return 'medium';
      case 'CANCELLED':
        return 'danger';
      case 'REJECTED':
        return 'danger';
      case 'EXPIRED':
        return 'danger';
      case 'EXTENDED':
        return 'tertiary';
      default:
        return 'medium';
    }
  }

  async openGoogleMaps(tag: string) {
    if (!tag) {
      console.log('No TAG provided to openGoogleMaps');
      return;
    }

    try {
      // Get structure details for the tag
      const structureDetails = await this.getStructureDetails(tag);

      if (structureDetails && structureDetails.LATITUDE && structureDetails.LONGITUDE) {
        const lat = structureDetails.LATITUDE;
        const lng = structureDetails.LONGITUDE;

        // Create Google Maps URL
        const googleMapsUrl = `https://www.google.com/maps?q=${lat},${lng}`;

        // Open in new window/tab
        window.open(googleMapsUrl, '_blank');
      } else {
        console.log('No location coordinates found for this tag');
        // Optionally show a toast message to the user
        await this.showAlert('Location Not Found', 'No location coordinates are available for this tag.');
      }
    } catch (error) {
      console.error('Error opening Google Maps:', error);
      await this.showAlert('Error', 'Unable to open location in Google Maps.');
    }
  }

  async navigateToLocation(tag: string) {
    if (!tag) {
      console.log('No TAG provided to navigateToLocation');
      return;
    }

    try {
      // Get structure details for the tag
      const structureDetails = await this.getStructureDetails(tag);

      if (structureDetails && structureDetails.LATITUDE && structureDetails.LONGITUDE) {
        const destinationLat = structureDetails.LATITUDE;
        const destinationLng = structureDetails.LONGITUDE;

        // Try to get current location first
        try {
          let currentLocation = null;

          // Check if we're on mobile or browser
          if (this.devicePlatform === 'browser') {
            // Browser - use web geolocation
            const position = await this.getCurrentPositionPromise();
            currentLocation = {
              lat: position.coords.latitude,
              lng: position.coords.longitude
            };
          } else {
            // Mobile - use Cordova geolocation
            const opt = { enableHighAccuracy: true, timeout: 5000, maximumAge: 0 };
            const position = await this.geolocation.getCurrentPosition(opt);
            currentLocation = {
              lat: position.coords.latitude,
              lng: position.coords.longitude
            };
          }

          // Create Google Maps navigation URL with current location as origin
          const navigationUrl = `https://www.google.com/maps/dir/${currentLocation.lat},${currentLocation.lng}/${destinationLat},${destinationLng}`;

          // Open navigation in new window/tab
          window.open(navigationUrl, '_blank');

        } catch (locationError) {
          console.log('Could not get current location, opening navigation without origin:', locationError);

          // Fallback: Open navigation without current location (Google Maps will ask for location permission)
          const navigationUrl = `https://www.google.com/maps/dir//${destinationLat},${destinationLng}`;
          window.open(navigationUrl, '_blank');
        }
      } else {
        console.log('No location coordinates found for this tag');
        await this.showAlert('Location Not Found', 'No location coordinates are available for this tag.');
      }
    } catch (error) {
      console.error('Error opening navigation:', error);
      await this.showAlert('Error', 'Unable to open navigation to this location.');
    }
  }

  // Helper method to promisify browser geolocation
  private getCurrentPositionPromise(): Promise<GeolocationPosition> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by this browser.'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => resolve(position),
        (error) => reject(error),
        {
          enableHighAccuracy: true,
          timeout: 5000,
          maximumAge: 0
        }
      );
    });
  }

  // Check if user text should be clickable (not black color)
  isUserClickable(stakeHolder: any): boolean {
    // If user dropdown is disabled, text is black and not clickable
    if (this.isDissabledUserDropdown(stakeHolder)) {
      return false;
    }

    // If user dropdown is enabled, check the color conditions
    const isClickableCondition = stakeHolder.APPROVAL == 'O' &&
                                 !this.isHideNewStakeHolderButton &&
                                 !this.isPermitIsExpired(this.permit) &&
                                 this.permit.APPROVAL_SUMMARY != 'R';

    return isClickableCondition;
  }

  // Get user skills with certificates for tooltip
  async getUserSkills(userId: string): Promise<any[]> {
    try {
      if (!userId) {
        return [];
      }

      // Query user skills from database with proper join
      const userSkillQuery = `SELECT us.SKILL_TYPE, us.RATING, us.USER_ID, sh.DESCRIPTION
                             FROM USER_SKILL us
                             LEFT JOIN SKILL_HEADER sh ON us.SKILL_TYPE = sh.SKILL_TYPE
                             WHERE us.USER_ID = '${userId}'
                             AND (us.P_MODE IS NULL OR us.P_MODE != 'D')
                             ORDER BY us.SKILL_TYPE`;

      const skillResult = await this.unviredSDK.dbExecuteStatement(userSkillQuery);

      if (skillResult && skillResult.data && skillResult.data.length > 0) {
        // Get skills with certificates
        const skillsWithCertificates = await Promise.all(
          skillResult.data.map(async (skill) => {
            // Query certificates for this skill using USER_DOC table
            const userDocQuery = `SELECT dh.DOC_ID, dh.FILE_NAME, dh.MIME_TYPE, dh.CREATED_ON, ud.DOC_CTX
                                 FROM DOCUMENT_HEADER dh
                                 JOIN USER_DOC ud ON dh.DOC_ID = ud.DOC_ID
                                 WHERE ud.USER_ID = '${userId}'
                                 AND (ud.P_MODE IS NULL OR ud.P_MODE != 'D')
                                 ORDER BY dh.CREATED_ON DESC`;

            const userDocResult = await this.unviredSDK.dbExecuteStatement(userDocQuery);

            let certificates = [];

            if (userDocResult && userDocResult.data) {
              // Filter certificates by skill type from DOC_CTX
              certificates = userDocResult.data.filter(doc => {
                try {
                  if (doc.DOC_CTX) {
                    const docCtx = JSON.parse(doc.DOC_CTX);
                    return docCtx.skillType === skill.SKILL_TYPE;
                  }
                  return false;
                } catch (e) {
                  console.error('Error parsing DOC_CTX:', e);
                  return false;
                }
              }).map(cert => ({
                attachmentId: cert.DOC_ID,
                fileName: cert.FILE_NAME,
                mimeType: cert.MIME_TYPE,
                createdOn: cert.CREATED_ON
              }));
            }

            return {
              skillType: skill.SKILL_TYPE,
              description: skill.DESCRIPTION || skill.SKILL_TYPE,
              rating: parseInt(skill.RATING) || 0,
              certificates: certificates
            };
          })
        );

        return skillsWithCertificates;
      }

      return [];
    } catch (error) {
      console.error('Error fetching user skills:', error);
      return [];
    }
  }

  // Handle user text click with restriction
  async onUserTextClick(stakeHolder: any, index: number, event: Event) {
    event.stopPropagation();

    // Check if click is allowed
    if (!this.isUserClickable(stakeHolder)) {
      console.log('User text click is disabled for this stakeholder');
      return;
    }

    // Proceed with normal click behavior
    if (this.permit.APPROVAL_SUMMARY !== 'R' && !this.isHideNewStakeHolderButton) {
      this.addNewStakeHolderRole(true, index, stakeHolder);
    }
  }

  // Open certificate file
  async openCertificate(certificate: any) {
    try {
      console.log('Opening certificate:', certificate);

      if (this.attachmentService && certificate.attachmentId) {
        // Create attachment object for the service
        const attachmentObj = {
          UID: certificate.attachmentId,
          ATTACHMENT_ID: certificate.attachmentId,
          FILE_NAME: certificate.fileName,
          MIME_TYPE: certificate.mimeType
        };

        // Use the downloadAndWriteAttachmentToFile method to download and open
        const response = await this.attachmentService.downloadAndWriteAttachmentToFile(
          attachmentObj,
          certificate.fileName
        );

        if (response && response.body) {
          // Create blob and open in new tab
          const mimeType = certificate.mimeType || 'application/pdf';
          const blob = new Blob([response.body], { type: mimeType });
          const url = window.URL.createObjectURL(blob);
          window.open(url, '_blank');

          // Clean up the URL after a delay
          setTimeout(() => {
            window.URL.revokeObjectURL(url);
          }, 1000);
        }
      } else {
        console.warn('Attachment service not available or invalid certificate data');
      }
    } catch (error) {
      console.error('Error opening certificate:', error);
    }
  }

  // Show user skills tooltip
  async showUserSkillsTooltip(event: Event, userId: string) {
    event.stopPropagation();

    try {
      const skills = await this.getUserSkills(userId);

      // Show skills in popover with better positioning
      const popover = await this.popoverCtrl.create({
        component: UserSkillsTooltipComponent,
        componentProps: {
          skills: skills,
          userName: this.getName(userId),
          onCertificateClick: (certificate: any) => this.openCertificate(certificate)
        },
        event: event, // Keep event for initial positioning
        translucent: true,
        showBackdrop: true,
        backdropDismiss: true,
        cssClass: 'skills-tooltip-popover',
        // Better positioning - try different sides based on screen position
        side: 'start', // This will position to the left
        alignment: 'center'
      });

      await popover.present();
    } catch (error) {
      console.error('Error showing user skills tooltip:', error);
    }
  }

  private async getStructureDetails(tag: string) {
    if (!tag) {
      console.log('No TAG provided to getStructureDetails');
      return null;
    }

    try {
      // The PERMIT_HEADER->TAG field is a reference to STRUCTURE_HEADER->TAG
      // We need to query the STRUCTURE_HEADER table to get the latitude and longitude
      const query = `SELECT * FROM STRUCTURE_HEADER WHERE TAG = '${tag}'`;
      console.log(`Executing query: ${query}`);
      const result = await this.unviredSDK.dbExecuteStatement(query);

      if (result?.data && result.data.length > 0) {
        console.log(`Found structure details for TAG ${tag}:`, result.data[0]);
        return result.data[0];
      }
      console.log(`No structure found for TAG ${tag}`);
      return null;
    } catch (error) {
      console.error('Error fetching structure details:', error);
      return null;
    }
  }

  // Property to track if coordinates are available for the current permit
  hasCoordinates: boolean = false;

  /**
   * Check if location coordinates are available for a given tag
   * @param tag The structure tag
   * @returns Promise<boolean> indicating if coordinates are available
   */
  async hasLocationCoordinates(tag: string): Promise<boolean> {
    if (!tag) {
      return false;
    }

    try {
      const structureDetails = await this.getStructureDetails(tag);
      return !!(structureDetails && structureDetails.LATITUDE && structureDetails.LONGITUDE);
    } catch (error) {
      console.error('Error checking location coordinates:', error);
      return false;
    }
  }

  /**
   * Check coordinates and update the hasCoordinates property
   */
  async checkAndSetCoordinates() {
    if (this.permit && this.permit.TAG) {
      this.hasCoordinates = await this.hasLocationCoordinates(this.permit.TAG);
    } else {
      this.hasCoordinates = false;
    }
  }

  /**
   * Handle location icon click with coordinate check
   */
  async onLocationClick() {
    if (this.hasCoordinates && this.permit && this.permit.TAG) {
      this.openGoogleMaps(this.permit.TAG);
    }
  }

  /**
   * Handle navigate icon click with coordinate check
   */
  async onNavigateClick() {
    if (this.hasCoordinates && this.permit && this.permit.TAG) {
      this.navigateToLocation(this.permit.TAG);
    }
  }


  async getAllUsers() {
    let getUsersQuery = '';
    if (this.permit.AGENT_ID_EXT != null && this.permit.AGENT_ID_INT != null) {
      getUsersQuery = `select B.first_name, B.last_name, B.USER_ID, B.role_name, A.is_internal
      from role_header as A join user_header as B on A.role_name=B.role_name
        and B.User_id in (select user_id from agent_user_header where agent_id in ('${this.permit.AGENT_ID_EXT}','${this.permit.AGENT_ID_INT}'))`;
    } else if (
      this.permit.AGENT_ID_EXT != null &&
      this.permit.AGENT_ID_INT == null
    ) {
      getUsersQuery = `select B.first_name, B.last_name, B.USER_ID, B.role_name, A.is_internal
      from role_header as A join user_header as B on A.role_name=B.role_name
        and B.User_id in (select user_id from agent_user_header where agent_id in ('${this.permit.AGENT_ID_EXT}'))`;
    } else {
      getUsersQuery = `select B.first_name, B.last_name, B.USER_ID, B.role_name,A.is_internal
      from role_header as A join user_header as B on A.role_name=B.role_name
        and B.User_id in (select user_id from agent_user_header where agent_id in ('${this.permit.AGENT_ID_INT}'))`;
    }
    let fetchgetUsersQueryResult = await this.unviredSDK.dbExecuteStatement(
      getUsersQuery
    );
    if (fetchgetUsersQueryResult?.data?.length > 0) {
      this.allUsers = fetchgetUsersQueryResult?.data;
      console.log("this.usersList", this.allUsers);
    }
  }





  shouldShowClipboardIcon(stakeholder: any): boolean {
    // console.log("shouldShowClipboardIcon" , stakeholder)
    return stakeholder.APPROVAL === 'O' &&
      stakeholder.ROLE === 'REVIEW' &&
      stakeholder.APPR_TYPE !== null &&
      this.formData?.components?.some(comp => comp.key === stakeholder.CONTEXT);
  }


  async checkAllFieldsAreField(stakeholder: any) {
    let val = await returnTempData();
    if (val) {
      let tempdata = val.tempData ? val.tempData : val.submission;
      // Check if context exists and validate its values
      if (tempdata && tempdata.data && tempdata.data[stakeholder.CONTEXT]) {
        let contextData = tempdata.data[stakeholder.CONTEXT];
        console.log("context data & temp data", contextData, tempdata)
        let hasEmptyValues = false;

        // Check all values in the context object
        Object.keys(contextData).forEach(key => {
          if (!contextData[key] || contextData[key] === '') {
            hasEmptyValues = true;
          }
        });
      }
    }
  }

  async onSegmentChange() {
    this.showSaveButton = this.segmentValue === 'form';
    let val = await returnTempData();
    if (val) {
      let tempdata = val.tempData ? val.tempData : val.submission;
      // console.log("tempdata on segment change" , this.formData , this.selectedStakeholder)
    }
    // let tempdata = val.tempData ? val.tempData : val.submission;
    let data = JSON.stringify(this.formData);
    if (this.previousSegmentValue === 'details' && this.segmentValue === 'form' && !this.actionButtonClicked && this.selectedStakeholder) {
      this.formReadOnlyFlag = true;
      this.formData = this.replaceApproverName(data, `${this.selectedStakeholder.APPR_TYPE}Name`, `${this.selectedStakeholder.USER_ID}`);
      // console.log("this.previousSegmentValue" , JSON.parse(this.formData) )
    }


    if (this.previousSegmentValue === 'form' && this.segmentValue === 'details') {
      // console.log('Tab switched from form to details');
      // Reset form data components to original state if needed

      // await this.createFormAndSetFormData()
      // console.log("this.formData.components is " , this.formData.components)
      // if (this.originalComponents?.length > 0) {
      //   this.formData.components = [...this.originalComponents];
      // }
    }
    this.previousSegmentValue = this.segmentValue;
  }

  replaceApproverName(jsonText: string, keyName: string, userId: string): string {
    try {
      // Parse the JSON string to an object
      const obj = JSON.parse(jsonText) as { components: FormComponent[] };

      // Recursive function to traverse the component tree
      let traverse = (component: FormComponent): void => {
        // Check if this is the component we're looking for
        if (component.key === keyName) {
          const contextName = keyName.replace('Name', '');

          // Initialize the object if it doesn't exist
          if (!this.subData[contextName]) {
            this.subData[contextName] = {};
          }
          if (component.key === keyName && component.defaultValue === "@ApproverName@" && this.actionButtonClicked) {
            // console.log(`Found match! Replacing @ApproverName@ with ${userId} in component ${keyName} && this.actionButtonClicked is ${this.actionButtonClicked}`);
            component.defaultValue = userId;
            this.subData[contextName][keyName] = userId;

          } else if (component.key === keyName && component.defaultValue == userId && this.actionButtonClicked == false) {
            // console.log(`tab switched with ${userId} in component ${keyName} && this.actionButtonClicked is ${this.actionButtonClicked}`);
            component.defaultValue = ''

            this.subData[contextName][keyName] = '';
          } else if (component.key === keyName && component.defaultValue === "@ApproverName@" && !this.actionButtonClicked) {
            // console.log(`tab switched with ${userId} in component ${keyName} && this.actionButtonClicked is ${this.actionButtonClicked}`);
            component.defaultValue = ''

            this.subData[contextName][keyName] = '';
          }
        }


        // Handle nested components array
        if (Array.isArray(component.components)) {
          component.components.forEach((child: FormComponent) => traverse(child));
        }

        // Handle columns structure which is common in form builders
        if (component.columns) {
          component.columns.forEach((column: Column) => {
            if (Array.isArray(column.components)) {
              column.components.forEach((child: FormComponent) => traverse(child));
            }
          });
        }
      }

      // Start traversal from the root components array
      if (Array.isArray(obj?.components)) {
        obj.components.forEach((component: FormComponent) => traverse(component));
      }

      // Convert back to JSON string with pretty formatting
      return JSON.stringify(obj);
    } catch (error) {
      console.error("Error in replaceApproverInComponent:", error);
      return jsonText; // Return original on error
    }
  }



  replaceApproverRadio(jsonText: string, keyName: string, status: string): string {
    try {
      // Parse the JSON string to an object
      const obj = JSON.parse(jsonText) as { components: FormComponent[] };

      // Recursive function to traverse the component tree
      let traverse = (component: FormComponent): void => {
        // Check if this is the component we're looking for
        if (component.key === keyName) {
          const contextName = keyName.replace('Radio', '');

          // Initialize the object if it doesn't exist
          if (!this.subData[contextName]) {
            this.subData[contextName] = {};
          }
          if (component.key === keyName && this.actionButtonClicked == true) {
            // console.log(`Found match! Replacing @ApproverName@ with ${userId} in component ${keyName} && this.actionButtonClicked is ${this.actionButtonClicked}`);
            // component.defaultValue = userId;
            console.log("the status in 1 is ", status)
            this.subData[contextName][keyName] = status;

          } else if (component.key === keyName && this.actionButtonClicked == false) {
            // console.log(`tab switched with ${userId} in component ${keyName} && this.actionButtonClicked is ${this.actionButtonClicked}`);
            // component.defaultValue = ''
            console.log("the status in 2 is ", status)
            this.subData[contextName][keyName] = '';
          } else if (component.key === keyName && !this.actionButtonClicked) {
            // console.log(`tab switched with ${userId} in component ${keyName} && this.actionButtonClicked is ${this.actionButtonClicked}`);
            component.defaultValue = ''
            console.log("the status in 3 is ", status)
            // this.subData[contextName][keyName] = '';
          }
        }


        // Handle nested components array
        if (Array.isArray(component.components)) {
          component.components.forEach((child: FormComponent) => traverse(child));
        }

        // Handle columns structure which is common in form builders
        if (component.columns) {
          component.columns.forEach((column: Column) => {
            if (Array.isArray(column.components)) {
              column.components.forEach((child: FormComponent) => traverse(child));
            }
          });
        }
      }

      // Start traversal from the root components array
      if (Array.isArray(obj?.components)) {
        obj.components.forEach((component: FormComponent) => traverse(component));
      }

      // Convert back to JSON string with pretty formatting
      return JSON.stringify(obj);
    } catch (error) {
      console.error("Error in replaceApproverInComponent:", error);
      return jsonText; // Return original on error
    }
  }

  searchForApprovalType(formDataComp: any, appr_type: string): boolean {
    try {
      let hasMatchingApprovalType = false;

      // Recursive function to traverse the component tree
      const traverse = (component: FormComponent): void => {
        // Check if this is the component we're looking for
        if (component.key === appr_type) {
          hasMatchingApprovalType = true;
          return;
        }

        // Handle nested components array
        if (Array.isArray(component.components)) {
          component.components.forEach(child => traverse(child));
        }

        // Handle columns structure
        if (component.columns) {
          component.columns.forEach(column => {
            if (Array.isArray(column.components)) {
              column.components.forEach(child => traverse(child));
            }
          });
        }
      };

      // Start traversal from the root components array
      if (Array.isArray(formDataComp?.components)) {
        formDataComp.components.forEach(component => traverse(component));
      }

      return hasMatchingApprovalType;

    } catch (error) {
      console.error("Error in searchForApprovalType:", error);
      return false; // Return false on error
    }
  }


  async generalupdatePermitLog(stakeArray: any) {
    console.log("the stake array is ", stakeArray)
    for (let i = 0; i < stakeArray.length; i++) {

      let stakeObj = this.stakeHoldersList.find((ele) => ele.ROW_ID == stakeArray[i].ROW_ID)
      console.log("item that is rejected", stakeArray[i])
      console.log("created new permit log")
      let permitLogObj = new PERMIT_LOG();
      permitLogObj.PERMIT_NO = stakeObj.PERMIT_NO;
      permitLogObj.OBJECT_STATUS = 1;
      permitLogObj.P_MODE = 'A';
      permitLogObj.FID = this.permit.LID;
      permitLogObj.APPR_TYPE = stakeObj.APPR_TYPE
      permitLogObj.LOG_NO = await this.findMaxLogNumberFromDb(
        this.permit.PERMIT_NO
      );

      if (stakeObj.ROLE == 'REVIEW' || stakeObj.ROLE == 'EXECUTE') {
        permitLogObj.ACTION = 'CONFIRM';
      } else {
        permitLogObj.ACTION = 'TRANSITION';
      }

      permitLogObj.CREATED_ON = new Date().getTime();


      permitLogObj.CREATED_BY = `${this.userObject.USER_ID}`;
      if (stakeArray[i].status == 'Approved') {

        // this.permit.isRejected = true
        permitLogObj.PERMIT_STATUS = 'IN_REVIEW'
        permitLogObj.APPROVAL = 'A'
        this.permit.APPROVAL_SUMMARY = 'A'

      } else if (stakeArray[i].status == 'Rejected') {

        permitLogObj.COMMENT = stakeArray[i].comment

        // this.permit.isRejected = true
        permitLogObj.PERMIT_STATUS = 'IN_REVIEW'
        permitLogObj.APPROVAL = 'R'
        this.permit.APPROVAL_SUMMARY = 'R'


      } else if (stakeArray[i].status == 'Executed') {
        permitLogObj.PERMIT_STATUS = 'IN_REVIEW';
        permitLogObj.APPROVAL = 'A'
      } else if (stakeArray[i].status == 'Confirmed') {
        permitLogObj.PERMIT_STATUS = 'APPROVED';
          permitLogObj.APPROVAL = 'A'
    } else if(stakeArray[i].status == 'Closed'){
      permitLogObj.PERMIT_STATUS = 'CLOSED';
        permitLogObj.APPROVAL = 'A'
  }
    
    console.log("the permit log obj inserted is" , permitLogObj)
    let result = await this.unviredSDK.dbInsert('PERMIT_LOG', permitLogObj, false)
    if (result.type == ResultType.success) {

        this.rejectionCompleted = true;
      } else {
        console.error("Error while approving the action")
      }





    }
  }



isWithin8Hours(): boolean {

  if(this.userRoleData[0].EXTEND == 'true'){
    const now = new Date().getTime(); // Current time in milliseconds
    const expiryDate = new Date(this.permit.EXPIRY_DATE).getTime(); // Convert permit.EXPIRY_DATE to milliseconds
    return now < expiryDate + 8 * 60 * 60 * 1000; // Check if current time is less than 8 hours from expiry date
  }else {
    return false;
  }
 
}


checkIfPermitHasStarted(permit: any): boolean {
  const now = new Date().getTime(); 
  const startDate = new Date(permit.PERMIT_DATE).getTime();
  return permit.STATUS === 'ISSUED' && now >= startDate;
}

setStartDateToNow() {
    const now = new Date();
    const localISOString = this.formatDateToLocalISOString(now);
    this.extensionForm.controls['startDate'].setValue(localISOString);
  }


  checkIfCloseRoleInStakeHolders(): boolean {
      const reviewStakeholders = this.stakeHoldersList.filter(s => s.ROLE === 'REVIEW' && s.APPR_TYPE == null);
      const closeStakeholder = this.stakeHoldersList.filter(stakeholder => stakeholder.ROLE === 'CLOSE');
      if(reviewStakeholders.length == closeStakeholder.length){
        return true;
      } else {
        return false;
      }
  }

  // Check if the current logged-in user is assigned to a CLOSE role
  isCurrentUserAssignedToCloseRole(): boolean {
    if (!this.userObject || !this.stakeHoldersList) {
      return false;
    }

    return this.stakeHoldersList.some(stakeholder =>
      stakeholder.ROLE === 'CLOSE' &&
      stakeholder.USER_ID === this.userObject.USER_ID
    );
  }

  async discardLocalChanges() {
  // Revert all modified stakeholders for this permit
  // Set P_MODE and OBJECT_STATUS back to original for modified rows
  await this.unviredSDK.dbExecuteStatement(
    `UPDATE PERMIT_STAKEHOLDER SET P_MODE = NULL, OBJECT_STATUS = 1 WHERE PERMIT_NO = '${this.permit.PERMIT_NO}' AND (P_MODE = 'M' OR OBJECT_STATUS = 2)`
  );
  // Optionally, remove any added stakeholders that are not yet synced
  await this.unviredSDK.dbExecuteStatement(
    `DELETE FROM PERMIT_STAKEHOLDER WHERE PERMIT_NO = '${this.permit.PERMIT_NO}' AND P_MODE = 'A'`
  );
  // Optionally, reload the stakeholders list
  await this.getAllStakeHolders();
}





 


}

