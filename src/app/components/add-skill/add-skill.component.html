<div>
<ion-header>
  <ion-toolbar color="primary">
    <ion-title style="font-size: large;">{{ isEditMode ? 'Edit User Skill' : 'Add New Skill' }}</ion-title>
    <ion-buttons slot="start" mode="ios" style="padding-right: 10px;">
      <ion-button (click)="cancel()">
        <ion-icon slot="icon-only" name="arrow-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>
  <form [formGroup]="skillForm">
    <div *ngIf="isEditMode" class="form-container">
      <!-- Skill Input - Full Width -->
      <div class="form-group">
        <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 15px;"
          [value]="selectedSkill.SKILL_TYPE" readonly
          labelPlacement="stacked" fill="outline">
          <div slot="label">Skill</div>
          <ion-icon slot="start" name="school-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
        </ion-input>
      </div>

      <!-- Rating Input - Full Width with Outline -->
      <div class="form-group">
        <div class="rating-wrapper" style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 15px;">
          <div class="rating-label">Rating</div>
          <div class="rating-content">
            <ion-icon name="star-half-outline" class="rating-icon"></ion-icon>
            <div class="rating-edit">
              <ng-container *ngFor="let star of [1, 2, 3, 4, 5]; let j = index">
                <ion-icon [name]="j < skillForm.get('rating')?.value ? 'star' : 'star-outline'"
                        (click)="updateRating(j + 1)" class="star-icon">
                </ion-icon>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
      <div class="section-header">
        <h4>Attachments & Links</h4>
        <div>
          <ion-button fill="clear" (click)="fileInput.click()">
            <ion-icon name="attach-outline" style="margin-right: 5px;"></ion-icon>
            Add Attachment
          </ion-button>
          <ion-button fill="clear" (click)="showLinkForm = !showLinkForm">
            <ion-icon name="link-outline" style="margin-right: 5px;"></ion-icon>
            Add Hyperlink
          </ion-button>
        </div>
      </div>
        <div class="attachments-table">
          <ion-list lines="full">
            <!-- Display Uploaded Certificates & Links -->
            <ng-container *ngFor="let image of getFilteredDocs(selectedSkill.SKILL_TYPE); index as j">
              <ion-item *ngIf="image?.docItem?.FILE_NAME" class="attachment-item">
                <!-- Document icon based on type -->
                <ion-icon slot="start"
                  [name]="image?.DOC_TYPE == 'URL' ? 'link-outline' : 'document-outline'"
                  style="color: #00629b; font-size: 20px;"></ion-icon>

                <!-- Excel file -->
                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'EXCEL'"
                  (click)="downloadFile(image)" class="attachment-name">
                  {{ image.docItem.FILE_NAME }}
                </a>

                <!-- PowerPoint file -->
                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'PPT'"
                  (click)="downloadFile(image)" class="attachment-name">
                  {{ image.docItem.FILE_NAME }}
                </a>

                <!-- Word file -->
                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && (image?.DOC_TYPE == 'WORD' || image?.DOC_TYPE == 'DOCUMENT')"
                  (click)="downloadFile(image)" class="attachment-name">
                  {{ image.docItem.FILE_NAME }}
                </a>

                <!-- PDF file -->
                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'PDF'"
                  (click)="downloadFile(image)" class="attachment-name">
                  {{ image.docItem.FILE_NAME }}
                </a>

                <!-- Text file -->
                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'TEXT'"
                  (click)="downloadFile(image)" class="attachment-name">
                  {{ image.docItem.FILE_NAME }}
                </a>

                <!-- CSV file -->
                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'CSV'"
                  (click)="downloadFile(image)" class="attachment-name">
                  {{ image.docItem.FILE_NAME }}
                </a>

                <!-- URL link -->
                <a *ngIf="(image.thumbnail == '' || image.thumbnail == null) && image?.DOC_TYPE == 'URL'"
                  [href]="image.docItem.FILE_NAME" target="_blank" rel="noopener noreferrer" class="link-item">
                  {{ image.docItem.TITLE }}
                </a>

                <!-- Image file -->
                <span *ngIf="image?.DOC_TYPE === 'IMAGE'"
                  (click)="image?.docItem ? attachmentService.imageInFullscreen(image, 'Certificate Image: ', j) : attachmentService.editImage(image, j)"
                  class="attachment-name">
                  {{ image?.docItem?.FILE_NAME }}
                </span>

                <!-- Delete button -->
                <ion-button fill="clear" (click)="deleteSelectedCertificate(j, 1)" slot="end" class="delete-button">
                  <ion-icon name="trash-outline" color="danger"></ion-icon>
                </ion-button>
              </ion-item>
            </ng-container>

            <!-- Empty state message -->
            <ion-item *ngIf="getFilteredDocs(selectedSkill.SKILL_TYPE).length === 0" lines="none" class="empty-message">
              <ion-label color="medium" class="ion-text-center">
                No attachments or links added yet.
              </ion-label>
            </ion-item>
          </ion-list>
        </div>
      <!-- </ion-card> -->
      <ion-card *ngIf="showLinkForm" class="link-form-card">
        <ion-card-header>
          <ion-card-title>Add Hyperlink</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <form [formGroup]="linkForm">
            <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 10px;"
              placeholder="Enter Link Name" formControlName="linkname"
              labelPlacement="stacked" fill="outline">
              <div slot="label">Link Name</div>
              <ion-icon slot="start" name="text-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
            </ion-input>

            <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 10px;"
              placeholder="Enter URL (e.g., https://example.com)" formControlName="linkurl"
              labelPlacement="stacked" fill="outline">
              <div slot="label">Link URL</div>
              <ion-icon slot="start" name="globe-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
            </ion-input>

            <div class="button-group">
              <ion-button (click)="saveLink()" [disabled]="!linkForm.valid">Apply</ion-button>
              <ion-button fill="outline" (click)="showLinkForm = false">Cancel</ion-button>
            </div>
          </form>
        </ion-card-content>
      </ion-card>
    </div>
    <div *ngIf="!isEditMode" class="form-container">
      <!-- Skill Dropdown - Full Width -->
      <div class="form-group">
        <ion-select style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 15px;"
          interface="popover" placeholder="Select Skill" fill="outline"
          formControlName="skillType" labelPlacement="stacked">
          <div slot="label">Skill <ion-text color="danger">*</ion-text></div>
          <ion-icon slot="start" name="school-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
          <ion-select-option *ngFor="let skill of filteredSkillList" [value]="skill.SKILL_TYPE">
            {{ skill.DESCRIPTION }}
          </ion-select-option>
        </ion-select>
      </div>

      <!-- Rating Input - Full Width with Outline -->
      <div class="form-group">
        <div class="rating-wrapper" style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 15px;">
          <div class="rating-label">Rating</div>
          <div class="rating-content">
            <ion-icon name="star-half-outline" class="rating-icon"></ion-icon>
            <div class="rating-edit">
              <ng-container *ngFor="let star of [1, 2, 3, 4, 5]; let j = index">
                <ion-icon [name]="j < skillForm.get('rating')?.value ? 'star' : 'star-outline'"
                        (click)="updateRating(j + 1)" class="star-icon">
                </ion-icon>
              </ng-container>
            </div>
          </div>
        </div>
      </div>

      <div class="section-header">
        <h4>Attachments & Links</h4>
        <div>
          <ion-button fill="clear" (click)="fileInput.click()">
            <ion-icon name="attach-outline" style="margin-right: 5px;"></ion-icon>
            Add Attachment
          </ion-button>
          <ion-button fill="clear" (click)="showLinkForm = !showLinkForm">
            <ion-icon name="link-outline" style="margin-right: 5px;"></ion-icon>
            Add Hyperlink
          </ion-button>
        </div>
      </div>
        <div class="attachments-table">
          <ion-list lines="full">
            <!-- Display Uploaded Certificates & Links -->
            <ion-item *ngFor="let item of combinedList; index as j" class="attachment-item">
              <ion-icon slot="start" [name]="item.type == 'text/uri-list' ? 'link-outline' : 'document-outline'"
                style="color: #00629b; font-size: 20px;"></ion-icon>

              <ion-label *ngIf="item.type != 'text/uri-list'" class="attachment-name">
                {{item.name}}
              </ion-label>

              <a *ngIf="item.type == 'text/uri-list'" [href]="item.name" target="_blank" rel="noopener noreferrer" class="link-item">
                <ion-label>
                  {{ item.urlName }}
                </ion-label>
              </a>

              <ion-button fill="clear" (click)="deleteSelectedCertificate(j, 1)" slot="end" class="delete-button">
                <ion-icon name="trash-outline" color="danger"></ion-icon>
              </ion-button>
            </ion-item>

            <!-- Empty state message -->
            <ion-item *ngIf="combinedList.length === 0" lines="none" class="empty-message">
              <ion-label color="medium" class="ion-text-center">
                No attachments or links added yet.
              </ion-label>
            </ion-item>
          </ion-list>
        </div>
        <ion-card *ngIf="showLinkForm" class="link-form-card">
          <ion-card-header>
            <ion-card-title>Add Hyperlink</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <form [formGroup]="linkForm">
              <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 10px;"
                placeholder="Enter Link Name" formControlName="linkname"
                labelPlacement="stacked" fill="outline">
                <div slot="label">Link Name</div>
                <ion-icon slot="start" name="text-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
              </ion-input>

              <ion-input style="--padding-start: 16px !important;min-height: 38px;margin-bottom: 10px;"
                placeholder="Enter URL (e.g., https://example.com)" formControlName="linkurl"
                labelPlacement="stacked" fill="outline">
                <div slot="label">Link URL</div>
                <ion-icon slot="start" name="globe-outline" style="margin-right: 8px; color: #00629b;"></ion-icon>
              </ion-input>

              <div class="button-group">
                <ion-button (click)="saveLink()" [disabled]="!linkForm.valid">Apply</ion-button>
                <ion-button fill="outline" (click)="showLinkForm = false">Cancel</ion-button>
              </div>
            </form>
          </ion-card-content>
        </ion-card>
      </div>
    </form>

    <!-- File Upload Section -->
    <input type="file" #fileInput (change)="onFileSelected($event)" accept="*" multiple hidden />
</div>
<ion-footer mode="ios">
  <ion-toolbar mode="ios">
    <ion-button slot="end" color="danger" mode="md" (click)="cancel()">Cancel</ion-button>
    <ion-button slot="end" color="success" mode="md" (click)="saveSkill()" [disabled]="!skillForm.valid">Save</ion-button>
  </ion-toolbar>
</ion-footer>
