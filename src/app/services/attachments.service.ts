import {
  HttpClient,
  HttpHeaders,
  HttpParams,
  HttpResponse,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { AppConstants, PermitStatus } from '../shared/app-constants';
import { ConfigService } from './config.service';
import { Alert<PERSON>ontroller, ModalController, Platform } from '@ionic/angular';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { DomSanitizer } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import {
  RequestType,
  ResultType,
  UnviredCordovaSDK,
} from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { DataService } from './data.service';
import { DOCUMENT_ATTACHMENT } from '../data-models/data_classes';
import { firstValueFrom } from 'rxjs';
import { BusyIndicatorService } from './busy-indicator.service';
import { ImageFullScreenComponent } from '../components/image-full-screen/image-full-screen.component';
export enum AttachmentDownloadStatus {
  NoneAvailable,
  Pending,
  ReadyForDownload,
  Error,
}

@Injectable({
  providedIn: 'root',
})
export class AttachmentsService {
  private win: any = window;
  private isPDFDownloadRequestPresent: boolean = false;
  private isDownloadingPDF: boolean = false;
  private PDFDownloadIntervalCounter: number = 0;
  private PDFDownloadRetryInterval: number = 15; // In Seconds.
  private downloadInterval: any;
  private PDFAttachmentRequests: any[] = [];


  constructor(
    public unviredSDK: UnviredCordovaSDK,
    private httpClient: HttpClient,
    private dataService: DataService,
    private platform: Platform,
    private file: File,
    private _DomSanitizationService: DomSanitizer,
    private translate: TranslateService,
    private alertController: AlertController,
    public loader: BusyIndicatorService,
    public modalController: ModalController,
    public sanitizer: DomSanitizer,
    public device: Device,
    private configService: ConfigService
  ) {

  }

  async createAttachmentItem(
    id: string,
    path: string,
    file: any,
    Lid: string,
    name: string
  ) {
    let attachmentObject = new DOCUMENT_ATTACHMENT();
    attachmentObject.UID = id;
    attachmentObject.EXTERNAL_URL = '';
    attachmentObject.FILE_NAME = name;
    attachmentObject.LOCAL_PATH = path.substring(7, path.length);
    attachmentObject.ATTACHMENT_STATUS = 'SAVED_FOR_UPLOAD';
    attachmentObject.OBJECT_STATUS = 1;
    attachmentObject.FID = Lid;
    console.log('ATTACHMENT OBJECT' + JSON.stringify(attachmentObject));
    return await this.unviredSDK.createAttachmentItem(
      'DOCUMENT_ATTACHMENT',
      attachmentObject
    );
  }



  // Upload attachment Headers
   async uploadAttachment(inputHeader: any, type?) {
    try {
      let result;

      if (this.device.platform != 'browser') {
        let inspectionHeaderInput = {
          DOCUMENT_HEADER: inputHeader,
        };
        result = await this.unviredSDK.syncBackground(
          RequestType.RQST,
          inspectionHeaderInput,
          '',
          AppConstants.PA_MODIFY_DOCUMENT,
          'DOCUMENT',
          inputHeader.LID,
          false
        );
      } else {
        result = await this.unviredSDK.syncForeground(
          RequestType.RQST,
          '',
          inputHeader,
          AppConstants.PA_MODIFY_DOCUMENT,
          true
        );
      }

      if (result.type == ResultType.success) {
        this.unviredSDK.logInfo(
          'AttachmentsService',
          'uploadAttachment',
          'Uploaded file successfully.'
        );
      } else if (result.code && result.code === 401) {
        this.unviredSDK.logError(
          'AttachmentsService',
          'uploadAttachment',
          'Error while Uploaded file.'
        );
      } else {
        this.unviredSDK.logError(
          'AttachmentsService',
          'uploadAttachment',
          'Error while Uploaded file : ' + result.message
        );
      }
      return result;
    } catch (error) {
      this.unviredSDK.logError(
        'AttachmentsService',
        'uploadAttachment',
        'Error while Uploaded file : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }

  // Upload file as attachment to  server

  public async uploadFileToServer(file?: any): Promise<any> {
    let umpUrl = this.dataService.getUmpUrl(location.href);
    // PERMIT_token works only for web
    let token = `Bearer ${localStorage.getItem('PERMIT_token')}`;

    let headers = new HttpHeaders().append('Authorization', token);

    let formData = new FormData();
    if (file) {
        formData = new FormData();
        formData.append('file', file);
    }

    let guid = this.unviredSDK.guid().replace(/-/g, '');
    // Use config.json umpUrl if available, otherwise fall back to constants
    let url = this.dataService.getUmpUrl() + AppConstants.ATTACHMENT_URL + AppConstants.APP_NAME + '/attachments/' + guid;
    console.log('Attachment full URL:', url);

    return firstValueFrom(
        this.httpClient.post(url, formData, { headers: headers })
    );
}


  async sleep(timeIntervaal): Promise<void> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve();
      }, timeIntervaal);
    });
  }

  // async downloadAttachment(documentAttachment: any): Promise<any> {
  //   return await this.downloadAndWriteAttachmentToFile(documentAttachment);
  // }

  // Download attachment from server
  async downloadAndWriteAttachmentToFile(
    attachment: any,
    documentName?,
    type?
  ): Promise<any> {
    let token = `Bearer ${localStorage.getItem('PERMIT_token')}`;
    let umpUrl = this.dataService.getUmpUrl(location.href);
    let id = attachment.UID === undefined ? attachment.DOC_ID : attachment.UID;

    if (type == 'zip') {
      id = attachment.ATTACHMENT_ID;
    }

    console.log("id is " , id)
    // Use config.json umpUrl if available, otherwise fall back to constants
    var url =
      this.dataService.getUmpUrl() +
      AppConstants.ATTACHMENT_URL +
      AppConstants.APP_NAME +
      '/attachments/' +
      id;
    const httpOptions = {
      headers: new HttpHeaders({
        Authorization: token,
      }),
      observe: 'response' as 'body',
      responseType: 'blob' as 'blob',
    };

    console.log("url is " , url)

    try {
      const response: any = await this.httpClient
        .get(url, httpOptions)
        .toPromise();
      if (response) {
        if (documentName) {
          return response;
        } else if (!type) {
          let fileName = `${attachment.PROC_ID}_V${attachment.PROC_VER}`;
          if (fileName && fileName.length > 0) {
            fileName = `${fileName}.pdf`;
          } else {
            fileName = 'noname.pdf';
          }
          let res = this.saveBlobAsFile(response, fileName);
          return res;
        } else if (type) {
          // this.saveBlobAsFile(
          //   response,
          //   this.dataService.getReportFileName(attachment?.REQUEST_ID)
          // );
        }
      }
    } catch (error) {
      return { type: ResultType.error, error: error };
    }
  }

  private saveBlobAsFile(response: any, fileName: string) {
    if (response) {
      if (response.status == 200 || response.status == 202) {
        let blob: any = response.body;

        // IE10+ : (has Blob, but not a[download] or URL)
        // if (window.navigator.msSaveBlob) {
        //   return window.navigator.msSaveBlob(blob, fileName);
        // }

        var url = window.URL.createObjectURL(blob);
        var a = document.createElement('a');
        document.body.appendChild(a);
        a.href = url;
        a.download = fileName;
        a.click();
        window.URL.revokeObjectURL(url);
        return { type: ResultType.success, error: '' };
      } else if (response.status == 204) {
        return {
          type: ResultType.error,
          error: 'Attachment is added in queue.Please retry to download.',
        };
      }
    } else {
      return { type: ResultType.error, error: 'Error While Downloading File.' };
    }
  }

  // Write images into local file system.
  async writePicture(imageFile: any, name: string) {
    let filePath = '';
    if (this.platform.is('android')) {
      filePath = 'file:///storage/emulated/0/Pictures';
    } else if (this.platform.is('ios')) {
      filePath = this.file.documentsDirectory;
    }
    let writeFileResp = await this.file.writeFile(
      filePath,
      `${name}`,
      imageFile
    );
    console.log('File Writed Successfully', writeFileResp);
    return writeFileResp.nativeURL;
  }

  normalizeURL(url: string) {
    if (url == null || url.length == 0) {
      return '';
    }
    // Normalized Url starts with "http://"
    if (url.startsWith('http://')) return url;
    var updatedUrl = url;
    // For iOS, Extract the part after /Documents & construct the actual file URL.
    // in iOS, the sandbox folder location changes for every session.
    // Therefore we cannot us any absolute URL.
    if (this.platform.is('ios')) {
      let stringParts = updatedUrl.split('/Documents/');
      if (stringParts.length > 1) {
        let suffix = stringParts[1];
        updatedUrl = this.file.documentsDirectory + suffix;
      }
    }

    var fixedURL = this.win.Ionic.WebView.convertFileSrc(updatedUrl);
    fixedURL = this._DomSanitizationService.bypassSecurityTrustUrl(fixedURL);
    return fixedURL;
  }

  public isPDFDownloadRequestActive() {
    return this.isPDFDownloadRequestPresent;
  }

  public isCurrentlyMakingPDFDownloadRequest() {
    return this.isDownloadingPDF;
  }

  public getMaxAttempts(): number {
    return this.PDFDownloadRetryInterval;
  }
  public getSecondsCounter(): number {
    return this.PDFDownloadIntervalCounter;
  }



  getFilteredDocs(skillType: string, userDocData: any) {

    return userDocData.filter(image => {
      try {
        let docCtx = image?.docItem?.DOC_CTX ? JSON.parse(image.docItem.DOC_CTX) : null;
        return docCtx?.skillType === skillType;
      } catch (e) {
        console.error("Error parsing DOC_CTX:", e);
        return false;
      }
    });
  }



  openFile(item: any) {
    if (item.file) {
      // File is already available, create a URL and open in a new tab
      const blob = new Blob([item.file], { type: this.getDownloadFileExtension(item.DOC_TYPE) });
      const url = window.URL.createObjectURL(blob);
      window.open(url, '_blank');
    } else {
      // File needs to be fetched from the server
      this.downloadFile(item);
    }
  }



  getDownloadFileExtension(mimeType) {
    const mimeToExtMap = {
      'PDF': 'pdf',
      'EXCEL': 'xlsx',
      'TEXT':'txt',
      'WORD': 'docx',
      'DOCUMENT': 'docx',
      'PPT': 'pptx',
      'CSV': 'csv'
    };
    return mimeToExtMap[mimeType] || '.pdf';
  }


  async downloadFile(item: any) {
    let responseData: any;
    let response: any;
    let documentData: any;
    await this.loader.showBusyIndicator(this.translate.instant('Please wait') + '...', 'crescent');
    if (item.file === null) {
      documentData = {
        DOCUMENT: [
          {
            DOCUMENT_HEADER: {
              DOC_ID: item.docItem.DOC_ID,
            },
          },
        ],
      };

      if (this.dataService.getDevicePlatform() == 'browser') {
        response = await this.dataService.getDocument(documentData);
        if (response?.code && response.code === 404) {
          this.unviredSDK.logError(
            'InspectionDetailsComponent',
            'imageInFullscreen',
            'Error while Loading Image.'
          );
          if (this.loader.isLoading) {
            await this.loader.dismissBusyIndicator();
          }
        }
        if (response?.DOCUMENT_ATTACHMENT[0] !== undefined) {
          responseData =
            await this.downloadAndWriteAttachmentToFile(
              response.DOCUMENT_ATTACHMENT[0],
              item.docItem.DOC_ID
            );

          const blob = new Blob([responseData.body], { type: this.getDownloadFileExtension(item.DOC_TYPE) });
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = item.docItem.DOC_ID + '.' + this.getDownloadFileExtension(item.DOC_TYPE);
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
        }
      }
    } else {
      const blob = new Blob([item.file], { type: this.getDownloadFileExtension(item.DOC_TYPE) });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      // Use original filename if available, otherwise use DOC_ID with proper extension
      const fileName = item.docItem?.FILE_NAME || (item.docItem?.DOC_ID + '.' + this.getDownloadFileExtension(item.DOC_TYPE));
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    }

    if (this.loader.isLoading) {
      await this.loader.dismissBusyIndicator();
    }
  }



    async imageInFullscreen(item: any, fileName: any, index) {
      console.log("image in full screen called")
       let responseData: any;
       let downloadedImage: any;
       // let fileName: any = "";
       let base64data: any;
       let response: any;
       let documentData: any;
       await this.loader.showBusyIndicator(this.translate.instant('Please wait') + '...', 'crescent');
       if (item.file === null) {
         documentData = {
           DOCUMENT: [
             {
               DOCUMENT_HEADER: {
                 DOC_ID: item.docItem.DOC_ID,
               },
             },
           ],
         };

         // Fetch and display newly added image if has P_Mode A.
         if (item.docItem.P_MODE == 'A') {
           downloadedImage = this.sanitizer.bypassSecurityTrustResourceUrl(
             item.docItem.THUMBNAIL
           );
           // fileName = item.docItem.DOC_ID;
         } else {
           // For Browser
           if (this.dataService.getDevicePlatform() == 'browser') {
             response = await this.dataService.getDocument(documentData);
             if (response?.code && response.code === 404) {
               this.unviredSDK.logError(
                 'InspectionDetailsComponent',
                 'imageInFullscreen',
                 'Error while Loading Image.'
               );
               if (this.loader.isLoading) {
                 await this.loader.dismissBusyIndicator();
               }
             }
             if (response?.DOCUMENT_ATTACHMENT[0] !== undefined) {
               responseData =
                 await this.downloadAndWriteAttachmentToFile(
                   response.DOCUMENT_ATTACHMENT[0],
                   item.docItem.DOC_ID
                 );

               var reader = new FileReader();
               reader.readAsDataURL(responseData.body);
               reader.onload = async (_event) => {
                 base64data = reader.result;
                 // console.log(base64data);
                 downloadedImage = this.sanitizer.bypassSecurityTrustResourceUrl(
                   `${base64data}`
                 );
                 // fileName = item.docItem.DOC_ID;
               };
             }
           } else {
             // Fetch, download and display image if network present.
             // if (this.network.type != 'unknown' && this.network.type != 'none') {
             // For Mobile
             let fetchDocumentAttachmentQuery = `select * from document_attachment where fid IN (select LID from document_header where doc_id = '${item.docItem.DOC_ID}')`;
             let fetchDocumentAttachmentQueryResult: any =
               await this.unviredSDK.dbExecuteStatement(
                 fetchDocumentAttachmentQuery
               );
             if (
               fetchDocumentAttachmentQueryResult &&
               fetchDocumentAttachmentQueryResult.data.length > 0
             ) {
               // fileName = fetchDocumentAttachmentQueryResult.data[0].FILE_NAME;
               downloadedImage = this.normalizeURL(
                 'file://' +
                 fetchDocumentAttachmentQueryResult.data[0].LOCAL_PATH
               );
             } else {
               // Download and fetch attachment form doc attachments table and display image.
               response = await this.dataService.getDocument(documentData);
               if (response.DOCUMENT_ATTACHMENT[0] !== undefined) {
                 let fetchDocAttachmentQuery = `select * FROM DOCUMENT_ATTACHMENT WHERE UID = '${response.DOCUMENT_ATTACHMENT[0].UID}'`;
                 let fetchDocAttachmentQueryResult: any =
                   await this.unviredSDK.dbExecuteStatement(
                     fetchDocAttachmentQuery
                   );
                 if (
                   fetchDocAttachmentQueryResult &&
                   fetchDocAttachmentQueryResult.data.length > 0
                 ) {
                   let downloadedAttachment =
                     await this.unviredSDK.downloadAttachment(
                       'DOCUMENT_ATTACHMENT',
                       fetchDocAttachmentQueryResult.data[0]
                     );
                   if (downloadedAttachment.type == ResultType.success) {
                     if (downloadedAttachment.data.length > 0) {
                       await this.dataService.sleep(1000);
                       let fetchDocAttachmentQuery1 = `select * FROM DOCUMENT_ATTACHMENT WHERE UID = '${downloadedAttachment.data[0].UID}'`;
                       let fetchDocAttachmentQueryResult1: any =
                         await this.unviredSDK.dbExecuteStatement(
                           fetchDocAttachmentQuery1
                         );
                       if (
                         fetchDocAttachmentQueryResult1 &&
                         fetchDocAttachmentQueryResult1.data.length > 0
                       ) {
                         // fileName = fetchDocAttachmentQueryResult1.data[0].FILE_NAME;
                         downloadedImage = this.normalizeURL(
                           'file://' +
                           fetchDocAttachmentQueryResult1.data[0].LOCAL_PATH
                         );
                       }
                     }
                   }
                 }
               }
             }
             // }
             //  else {
             //   // Fetch and display image from inspection doc thumbnail if network not present.
             //   downloadedImage = this.sanitizer.bypassSecurityTrustResourceUrl(
             //     item.docItem.THUMBNAIL
             //   );
             // fileName = item.docItem.DOC_ID;
             // }
           }
         }
       } else {
         downloadedImage = item.thumbnail;
       }

       if (this.loader.isLoading) {
         await this.loader.dismissBusyIndicator();
       }
       await this.dataService.sleep(500);
       const modal = await this.modalController.create({
         cssClass: 'full-screen-modal',
         component: ImageFullScreenComponent,
         componentProps: {
           imagePath: downloadedImage,
           imageName: fileName + `${index + 1}`,
         },
       });
       await modal.present();
     }



          editImage(image: any , index: number) {
            // let img = document.getElementsByClassName("imagefull-display");
            // this.annotateImages(img[0]);
          }



  // checkPDFDownloadStatus() {
  //   console.log('Starting PDF Download..');
  //   this.isPDFDownloadRequestPresent = true;
  //   this.PDFDownloadIntervalCounter = 0;
  //   clearInterval(this.downloadInterval);
  //   this.startReportsDownloadRequest().then((result) => {
  //     switch (result) {
  //       case AttachmentDownloadStatus.Pending:
  //         console.log(
  //           'Download Pending. Try after ' +
  //             this.PDFDownloadRetryInterval +
  //             'seconds'
  //         );
  //         clearInterval(this.downloadInterval);
  //         this.downloadInterval = setInterval(() => {
  //           this.PDFDownloadIntervalCounter++;
  //           console.log(
  //             'Incrementing seconds counter:' + this.PDFDownloadIntervalCounter
  //           );
  //           if (
  //             this.PDFDownloadIntervalCounter == this.PDFDownloadRetryInterval
  //           ) {
  //             clearInterval(this.downloadInterval);
  //             this.checkPDFDownloadStatus();
  //           }
  //         }, 1000);
  //         break;

  //       case AttachmentDownloadStatus.ReadyForDownload:
  //         this.isPDFDownloadRequestPresent = false;

  //       case AttachmentDownloadStatus.Error:
  //       case AttachmentDownloadStatus.NoneAvailable:
  //         this.isPDFDownloadRequestPresent = false;

  //       default:
  //         break;
  //     }
  //   });
  // }

  // public startReportsDownloadRequest(): Promise<AttachmentDownloadStatus> {
  //   return new Promise(async (resolve, reject) => {
  //     /**
  //      * Make GET_PDF_REQUEST Call.
  //      * If the result is pending, return callback with true.
  //      * If the result is success, return callback with false.
  //      * If the result is error, return callback with false.
  //      */
  //     this.isDownloadingPDF = true;
  //     let result = await this.unviredSDK.syncForeground(
  //       RequestType.QUERY,
  //       '',
  //       '',
  //       AppConstants.PA_INSPECTION_REP_GET_REQUEST,
  //       true
  //     );
  //     this.isDownloadingPDF = false;
  //     if (result.type == ResultType.success) {
  //       let jsonData = result.data;
  //       if (!jsonData.REPORT_REQUEST) {
  //         resolve(AttachmentDownloadStatus.NoneAvailable);
  //         return;
  //       }
  //       this.PDFAttachmentRequests = jsonData.REPORT_REQUEST.map((data) => {
  //         return data.REPORT_REQUEST_HEADER;
  //       });
  //       this.PDFAttachmentRequests.sort((request1, request2) => {
  //         return request1.REQUEST_ID < request2.REQUEST_ID ? 1 : -1;
  //       });
  //       let requestStatuses = this.PDFAttachmentRequests.map((request) => {
  //         return request.REQUEST_STATUS;
  //       });
  //       if (requestStatuses.indexOf('ERROR') != -1) {
  //         resolve(AttachmentDownloadStatus.Error);
  //       } else if (requestStatuses.indexOf('OPEN') != -1) {
  //         resolve(AttachmentDownloadStatus.Pending);
  //       } else {
  //         resolve(AttachmentDownloadStatus.ReadyForDownload);
  //       }
  //     } else {
  //       console.log('ERROR while downloading PDF: ' + result.error);
  //       resolve(AttachmentDownloadStatus.Error);
  //     }
  //   });
  // }

  public getAllPDFRequests() {
    return this.PDFAttachmentRequests;
  }

  // cancelAttachment: Cancel the PDF Request made in the server.
  // public cancelAttachment(inputHeader: any): Promise<void> {
  //   let structureInput = {
  //     REPORT_REQUEST: [
  //       {
  //         REPORT_REQUEST_HEADER: inputHeader,
  //       },
  //     ],
  //   };

  //   return new Promise(async (resolve, reject) => {
  //     let result = await this.unviredSDK.syncForeground(
  //       RequestType.QUERY,
  //       '',
  //       structureInput,
  //       AppConstants.PA_INSPECTION_REP_CLOSE_REQUEST,
  //       true
  //     );
  //     if (result.type == ResultType.success) {
  //       let infoMessages = result.data.InfoMessage;
  //       if (infoMessages) {
  //         if (infoMessages.length > 0) {
  //           let message = infoMessages[0].message;
  //           let alert = this.alertController.create({
  //             header: this.translate.instant('ERROR'),
  //             message: message,
  //             buttons: [
  //               {
  //                 text: this.translate.instant('OK'),
  //               },
  //             ],
  //           });
  //           (await alert).present();
  //           reject(message);
  //           return;
  //         }
  //       }

  //       this.PDFAttachmentRequests = this.PDFAttachmentRequests.filter(
  //         (requestHeader) => {
  //           return requestHeader.REQUEST_ID != inputHeader.REQUEST_ID;
  //         }
  //       );
  //       // No More PDF attachment requests active.
  //       if (this.PDFAttachmentRequests.length == 0) {
  //         this.isPDFDownloadRequestPresent = false;
  //       }
  //       resolve();
  //     } else {
  //       console.log('ERROR cancelling Request: ' + result.error);
  //       reject();
  //     }
  //   });
  // }

  // async downloadCsvFromJson(jsonData, inputString, year?: any) {
  //   return new Promise((resolve, reject) => {
  //     console.log('Preparing CSV file from JSON data...');

  //     var temp = jsonData;
  //     switch (inputString) {
  //       case 'STRUCTURE':
  //         try {
  //           var structureRowString =
  //             '' +
  //             'Facility Id' +
  //             ',' +
  //             'Division Id' +
  //             ',' +
  //             'Tag' +
  //             ',' +
  //             'Category' +
  //             ',' +
  //             'Name' +
  //             ',' +
  //             'Status' +
  //             ',' +
  //             'Structure Type' +
  //             '\n';
  //           if (temp.STRUCTURE != undefined) {
  //             for (let row of temp.STRUCTURE) {
  //               structureRowString =
  //                 structureRowString +
  //                 row.STRUCTURE_HEADER.FACILITY_ID +
  //                 ',' +
  //                 row.STRUCTURE_HEADER.DIVISION_ID +
  //                 ',' +
  //                 `"${row.STRUCTURE_HEADER.TAG}"` +
  //                 ',' +
  //                 `"${row.STRUCTURE_HEADER.CATEGORY}"` +
  //                 ',' +
  //                 `"${row.STRUCTURE_HEADER.NAME}"` +
  //                 ',' +
  //                 `"${row.STRUCTURE_HEADER.STATUS}"` +
  //                 ',' +
  //                 `"${row.STRUCTURE_HEADER.STRUCT_TYPE}"` +
  //                 '\n';
  //             }
  //           }
  //           return resolve(
  //             this.downloadAsCSV(structureRowString, 'Structure.csv')
  //           );
  //         } catch (error) {
  //           console.log('*******', error);
  //           return reject(error);
  //         }
  //       case 'INSPECTION':
  //         try {
  //           if (temp.DEFICIENCY != undefined) {
  //             if (!temp.DEFICIENCY || !temp.DEFICIENCY.length) {
  //               return;
  //             }
  //             const separator = ',';
  //             const keys = [
  //               'Finding No',
  //               'Inspection ID',
  //               'Structure Tag',
  //               'Structure Description',
  //               'Findings',
  //               'Recommendations',
  //               'Rcmd. No.',
  //               'Priority',
  //               'Project No.',
  //               'Status of Repair',
  //               'Created By',
  //             ];
  //             const originalKeys = [
  //               'FINDING_NO',
  //               'INSP_ID',
  //               'TAG',
  //               'NAME',
  //               'FINDING_DESC',
  //               'RCA_DESC',
  //               'RCA_NO',
  //               'PRIO_CODE',
  //               'PROJECT_NO',
  //               'RCA_STATUS',
  //               'INSP_CREATED_BY',
  //             ];
  //             const csvData =
  //               keys.join(separator) +
  //               '\n' +
  //               temp.DEFICIENCY.map((row) => {
  //                 return originalKeys
  //                   .map((k) => {
  //                     let cell =
  //                       row.DEFICIENCY_HEADER[k] === null ||
  //                       row.DEFICIENCY_HEADER[k] === undefined
  //                         ? ''
  //                         : row.DEFICIENCY_HEADER[k];
  //                     cell =
  //                       cell instanceof Date
  //                         ? cell.toLocaleString()
  //                         : cell.toString().replace(/"/g, '""');
  //                     if (cell.search(/("|,|\n)/g) >= 0) {
  //                       cell = `"${cell}"`;
  //                     }
  //                     if (cell == 'OPEN') {
  //                       cell = 'Not Repaired';
  //                     } else if (cell == 'COMPLETED') {
  //                       cell = 'Repair Completed';
  //                     } else if (cell == 'REVIEWED') {
  //                       cell = 'Repair Verified';
  //                     }
  //                     return cell;
  //                   })
  //                   .join(separator);
  //               }).join('\n');
  //             return resolve(
  //               this.downloadAsCSV(csvData, 'Deficiency_report.csv')
  //             );
  //           }
  //         } catch (error) {
  //           console.log('*******', error);
  //           return reject(error);
  //         }
  //       case 'SCHEDULED_INSPECTION':
  //         try {
  //           var structureRowString =
  //             '' + 'Facility ID' + ',' + 'Structure Tag' + ',' + year;

  //           structureRowString = structureRowString + temp;
  //           return resolve(
  //             this.downloadAsCSV(
  //               structureRowString,
  //               'Scheduled_Inspections_Report.csv'
  //             )
  //           );
  //         } catch (error) {
  //           console.log('*******', error);
  //           return reject(error);
  //         }
  //     }
  //   });
  // }

  // async downloadAsCSV(csvString, filenames) {
  //   return new Promise((resolve, reject) => {
  //     console.log('CSV file is prepared. Downloading to the client');
  //     var csvFile;
  //     var downloadLink;
  //     csvFile = new Blob([csvString], { type: 'text/csv' });

  //     // IE10+ : (has Blob, but not a[download] or URL)
  //     if (navigator.msSaveBlob) {
  //       return navigator.msSaveBlob(csvFile, filenames);
  //     }

  //     downloadLink = document.createElement('a');
  //     downloadLink.download = filenames;
  //     let url = window.URL.createObjectURL(csvFile);
  //     downloadLink.href = url;
  //     downloadLink.style.display = 'none';
  //     document.body.appendChild(downloadLink);

  //     /**
  //      * Delay the download process to make sure that the DOM is built.
  //      */
  //     setTimeout(() => {
  //       downloadLink.click();

  //       document.body.removeChild(downloadLink);
  //       window.URL.revokeObjectURL(url);
  //       return resolve('Download process is complete');
  //     }, 1000);
  //   });
  // }
}
